import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Account } from '@/types/contracts/Account'
import { RemigrationBackup } from '@/types/contracts/remigration/RemigrationBackup'

wrappedTask('backupFinAccounts', 'backup all fin zone account data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { contract: accountContract } = await getContractWithSigner<Account>({ hre, contractName: 'Account' })
    const { contract: remigrationContract } = await getContractWithSigner<RemigrationBackup>({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup financial zone accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getFinAccountsAll' })

    const finAccounts: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await accountContract.getAccountCount()
    // console.log(`Total item: ${totalCount.toString()}`);

    while (finAccounts.length != Number(totalCount)) {
      if (finAccounts.length > Number(totalCount)) {
        console.error(`Error: Accounts count ${finAccounts.length} is greater than total count`)
        break
      }
      // [result, count, error]
      const [result, , err] = await remigrationContract.backupFinancialZoneAccounts(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${finAccounts.length + 1} ~ ${finAccounts.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${finAccounts.length + 1} ~ ${finAccounts.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const finAccount: any = []
          finAccount.push(result[key].accountId)
          const accountLimits = result[key].financialZoneAccountData.map((value) => value)
          finAccount.push(accountLimits)
          finAccounts.push(finAccount)
        }
      }
      offset++
    }

    // console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`);

    await saveBackupToJson({ data: finAccounts, fileName: 'finaccounts', networkName: hre.network.name })
  },
)
