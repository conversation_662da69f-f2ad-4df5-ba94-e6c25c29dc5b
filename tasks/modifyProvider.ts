import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('modifyProvider', 'modify provider', {
  filePath: path.basename(__filename),
})
  .addParam('provId', 'provider id')
  .addParam('provName', 'provider name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const provId = convertToHex({ hre, value: taskArguments.provId || '' })
    const provName = convertToHex({ hre, value: taskArguments.provName || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })
    contract.connect(kmsSigner)

    console.log(`*** provName更新: ${provId}`)
    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [provId, provName, deadline])

    await executeReceipt(contract.modProvider(provId, provName, traceId, deadline, kmsSig))
  })
