import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { AccessCtrl } from '@/types/contracts/AccessCtrl'
// npx hardhat checkAdminRole --network localFin --hash 0x00000000000000000000000000000000000000000000000000000000000000000 --deadline 1700000000 --signature
wrappedTask('checkAdminRole', 'Check if a signature has admin role.', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const hash = '0x43b221e00c0e930043d2de1a28685982d9ac398fbf20308f440316948d88abcc'

  const deadline = 1750922195
  const signature = new Uint8Array([
    94, 91, 40, 235, 249, 40, 98, 47, 183, 18, 189, 27, 51, 99, 32, 223, 36, 240, 75, 14, 154, 108, 191, 17, 106, 173,
    8, 39, 204, 233, 73, 40, 43, 119, 166, 255, 121, 123, 167, 110, 84, 212, 82, 208, 179, 243, 30, 114, 4, 12, 87, 188,
    151, 196, 197, 116, 187, 250, 193, 242, 65, 110, 216, 66, 28,
  ])

  console.log(`** checkAdminRole Parameters **\n`)

  try {
    const { contract } = await getContractWithSigner<AccessCtrl>({ hre, contractName: 'AccessCtrl' })

    const { has, err } = await contract.checkAdminRole(hash, deadline, signature)

    const formattedReceipt = {
      result: has ? 'ok' : 'failed',
      hasAdminRole: has,
      errorMessage: err,
    }

    console.log(`** checkAdminRole Result **\n`)
    printTable({ data: formattedReceipt })
  } catch (error) {
    showErrorDetails({ error })
  }
})
