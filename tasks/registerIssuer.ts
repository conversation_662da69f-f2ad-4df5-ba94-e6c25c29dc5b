import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('registerIssuer', 'register issuer', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('bankCode', 'bank code')
  .addParam('issuerName', 'issuer name')
  .addParam('issuerKey', 'issuer key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const { issuerKey = '', bankCode = '', issuerName = '', flag = '' } = taskArguments
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrIssuer = new hre.ethers.Wallet(issuerKey).address

    const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

    if (flag[0] === '1') {
      console.log(`*** issuerID登録: ${issuerId}`)
      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, issuerName, deadline],
      )
      await executeReceipt(contract.addIssuer(issuerId, bankCode, issuerName, traceId, deadline, kmsSig))
    }

    if (flag[1] === '1') {
      console.log(`*** issuer権限登録: ${issuerId}=${addrIssuer}`)
      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['uint256', 'address', 'uint256'], [issuerId, addrIssuer, deadline])
      await executeReceipt(contract.addIssuerRole(issuerId, addrIssuer, traceId, deadline, kmsSig))
    }
  })
