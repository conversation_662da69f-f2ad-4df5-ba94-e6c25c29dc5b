import { convertToHex } from '@tasks/common/convertToHex'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getContractWithSigner } from '@/tasks/common/getContractWithSigner'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('addValidatorAccountId', 'add validator accountId', { filePath: path.basename(__filename) })
  .addParam('validId', 'validetor id')
  .addParam('accountId', 'account id')
  .addParam('traceId', 'trace id')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const traceId = convertToHex({ hre, value: taskArguments.traceId || '' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    console.log(`*** バリデータが直接管理するアカウントID追加: ${accountId}`)

    await executeReceipt(contract.addValidatorAccountId(validatorId, accountId, traceId))
  })
