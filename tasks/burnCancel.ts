import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Token } from '@/types/contracts/Token'

wrappedTask('burnCancel', 'burn Cancel', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'amount')
  .addParam('blockTimestamp', 'block Timestamp')
  .addParam('issuerKey', 'issuer Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const amount = Number(taskArguments.amount || '')
      const blockTimestamp = Number(taskArguments.blockTimestamp || '')
      const issuerKey = taskArguments.issuerKey || ''
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** burnCancel Parameters **\n`)
      const params = {
        issuerId,
        accountId,
        amount,
        blockTimestamp,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Token>({ hre, contractName: 'Token' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
        [issuerId, accountId, amount, blockTimestamp, deadline],
      )

      await executeReceipt(contract.burnCancel(issuerId, accountId, amount, blockTimestamp, traceId, deadline, sig[0]))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
