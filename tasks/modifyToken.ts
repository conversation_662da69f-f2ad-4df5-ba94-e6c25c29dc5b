import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import * as PrivateKey from '@/privateKey'

wrappedTask('modifyToken', 'modify token', {
  filePath: path.basename(__filename),
})
  .addParam('provKey', 'provider key')
  .addParam('tokenId', 'token id')
  .addParam('tokenName', 'token name')
  .addParam('symbol', 'symbol')
  .setAction(async (taskArguments, hre) => {
    const providerKey = taskArguments.provKey
    const tokenId = convertToHex({ hre, value: taskArguments.tokenId || '' })
    const tokenName = convertToHex({ hre, value: taskArguments.tokenName || '' })
    const symbol = convertToHex({ hre, value: taskArguments.symbol || '' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    console.log(`*** Token登録情報更新: 0x${tokenId}`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(
      providerKey,
      ['bytes32', 'bytes32', 'bytes32', 'uint256'],
      [tokenId, tokenName, symbol, deadline],
    )

    await executeReceipt(contract.modToken(tokenId, tokenName, symbol, deadline, sig[0]))
  })
