import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('modTokenLimit', 'mod Token Limit', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('limitUpdates', 'limit updates')
  .addParam('limitValues', 'limit values')
  .addParam('issuerKey', 'issuer Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const limitUpdates = JSON.parse(taskArguments.limitUpdates || {})
      const limitValues = JSON.parse(taskArguments.limitValues || {})
      const issuerKey = taskArguments.issuerKey
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modTokenLimit Parameters **\n`)
      const params = {
        issuerId,
        accountId,
        limitUpdates,
        limitValues,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const deadline = await getTime()
      const sig = PrivateKey.sig(
        issuerKey,
        [
          'bytes32', // issuerId
          'uint256', // accountId
          // Struct AccountLimitUpdates
          {
            type: 'tuple',
            components: [
              { type: 'bool', name: 'mint' },
              { type: 'bool', name: 'burn' },
              { type: 'bool', name: 'charge' },
              { type: 'bool', name: 'discharge' },
              { type: 'bool', name: 'transfer' },
              // Nested struct CumulativeLimitUpdates
              {
                type: 'tuple',
                name: 'cumulative',
                components: [
                  { type: 'bool', name: 'total' },
                  { type: 'bool', name: 'mint' },
                  { type: 'bool', name: 'burn' },
                  { type: 'bool', name: 'charge' },
                  { type: 'bool', name: 'discharge' },
                  { type: 'bool', name: 'transfer' },
                ],
              },
            ],
          },
          // Struct AccountLimitValues
          {
            type: 'tuple',
            components: [
              { type: 'uint256', name: 'mint' },
              { type: 'uint256', name: 'burn' },
              { type: 'uint256', name: 'charge' },
              { type: 'uint256', name: 'discharge' },
              { type: 'uint256', name: 'transfer' },
              // Nested struct CumulativeLimitValues
              {
                type: 'tuple',
                name: 'cumulative',
                components: [
                  { type: 'uint256', name: 'total' },
                  { type: 'uint256', name: 'mint' },
                  { type: 'uint256', name: 'burn' },
                  { type: 'uint256', name: 'charge' },
                  { type: 'uint256', name: 'discharge' },
                  { type: 'uint256', name: 'transfer' },
                ],
              },
            ],
          },
          'uint256',
        ],
        [issuerId, accountId, limitUpdates, limitValues, deadline],
      )

      await executeReceipt(
        contract.modTokenLimit(issuerId,
          accountId,
          limitUpdates,
          limitValues,
          traceId,
          deadline,
          sig[0]),
      )
    } catch (error) {
      showErrorDetails({ error })
    }
  })
