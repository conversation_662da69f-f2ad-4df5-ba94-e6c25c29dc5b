import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { toBytes32 } from '@test/common/utils'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { AccountSyncBridge } from '@/types/contracts/AccountSyncBridge'
import { BalanceSyncBridge } from '@/types/contracts/BalanceSyncBridge'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('recoverPacket', 'set main contract address for bridge contract', {
  filePath: path.basename(__filename),
})
  .addParam('packetData', 'packet data')
  .addParam('contractName', 'contract name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })
    const { packetData = '', contractName = '' } = taskArguments

    let bridgeContract
    let deployer1
    let deadline
    let msgSalt
    let kmsSig

    if (contractName === 'AccountSyncBridge') {
      const { contract, deployer } = await getContractWithSigner<AccountSyncBridge>({
        hre,
        contractName: 'AccountSyncBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await getTime()) + 10
      msgSalt = toBytes32('accountSync')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else if (contractName === 'JPYTokenTransferBridge') {
      const { contract, deployer } = await getContractWithSigner<JPYTokenTransferBridge>({
        hre,
        contractName: 'JPYTokenTransferBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await getTime()) + 10
      msgSalt = toBytes32('jpyTokenTransfer')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else if (contractName === 'BalanceSyncBridge') {
      const { contract, deployer } = await getContractWithSigner<BalanceSyncBridge>({
        hre,
        contractName: 'BalanceSyncBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await getTime()) + 10
      msgSalt = toBytes32('balanceSync')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else {
      console.log('Invalid contract name')
      return
    }

    await executeReceipt(bridgeContract.recoverPacket(packetData, deployer1, deadline, kmsSig))
  })
