import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('modAccount', 'mod Account', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .addParam('accountName', 'account Name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const accountName = convertToHex({ hre, value: taskArguments.accountName || '' })
      const validatorKey = taskArguments.validatorKey || ''
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modAccount Parameters **\n`)
      const params = {
        validatorId,
        accountId,
        accountName,
        validatorKey,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'uint256'],
        [validatorId, accountId, deadline],
      )

      await executeReceipt(contract.modAccount(validatorId, accountId, accountName, traceId, deadline, sig[0]))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
