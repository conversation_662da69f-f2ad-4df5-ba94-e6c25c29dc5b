import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('registerBizZone', 'register biz zone', {
  filePath: path.basename(__filename),
})
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const { zoneId = '', zoneName = '' } = taskArguments

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })

    console.log(`*** biz zone登録: ${zoneId}`)
    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['uint16', 'string', 'uint256'], [zoneId, zoneName, deadline])

    await executeReceipt(contract.addBizZone(zoneId, zoneName, deadline, kmsSig))
  })
