import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime, showEthersRes } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('addIssuerRole', 'add issuer role', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = convertToHex({ hre, value: 'trace1' })
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const issuerKey = taskArguments.issuerKey || ''

    const addrIssuer = new hre.ethers.Wallet(issuerKey).address

    const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })
    contract.connect(kmsSigner)

    console.log(`*** add issuer role: ${issuerId}=${addrIssuer}`)

    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['uint256', 'address', 'uint256'], [issuerId, addrIssuer, deadline])

    await executeReceipt(contract.addIssuerRole(issuerId, addrIssuer, traceId, deadline, kmsSig))
  })
