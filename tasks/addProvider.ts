import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showEthersRes } from './common/tools'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('addProvider', 'add provider', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const provId = convertToHex({ hre, value: taskArguments.provId || '' })
    const zoneId = Number(taskArguments.zoneId || '')
    const zoneName = taskArguments.zoneName || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })
    contract.connect(kmsSigner)

    console.log(`** addProvider Parameters **\n`)
    const params = {
      provId,
      zoneId,
      zoneName,
    }
    printTable({ data: params })

    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'uint16', 'uint256'], [provId, zoneId, deadline])

    await executeReceipt(contract.addProvider(provId, zoneId, zoneName, traceId, deadline, kmsSig))
  })
