import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { printTable, showErrorDetails } from './common/tools'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'
// npx hardhat initializeTokenTransferBridge --network localFin --ibc-handler-address 0x0000000000000000000000000000000000000000 --ibc-token-address 0x0000000000000000000000000000 00000000000000000000000000000000 --access-ctrl-address 0x0000000000000000000000000000000000000000
wrappedTask('initializeTokenTransferBridge', 'Initialize JPY Token Transfer Bridge contract.', {
  filePath: path.basename(__filename),
})
  .addParam('ibcHandlerAddress', 'IBC Handler contract address')
  .addParam('ibcTokenAddress', 'IBC Token contract address')
  .addParam('accessCtrlAddress', 'Access Control contract address')
  .setAction(async (taskArguments, hre) => {
    const ibcHandlerAddress = taskArguments.ibcHandlerAddress || ''
    const ibcTokenAddress = taskArguments.ibcTokenAddress || ''
    const accessCtrlAddress = taskArguments.accessCtrlAddress || ''

    console.log(`** initializeTokenTransferBridge Parameters **\n`)
    const params = {
      ibcHandlerAddress,
      ibcTokenAddress,
      accessCtrlAddress,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
        hre,
        contractName: 'JPYTokenTransferBridge',
      })

      console.log(`*** Initializing JPYTokenTransferBridge contract...`)

      await executeReceipt(contract.initialize(ibcHandlerAddress, ibcTokenAddress, accessCtrlAddress))

      console.log(`*** JPYTokenTransferBridge initialization completed successfully!`)

      // Verify initialization by checking the version
      const version = await contract.version()
      const config = await contract.getConfig()

      const resultInfo = {
        contractVersion: version,
        sourcePort: config.port,
        sourceChannel: config.channel,
        sourceVersion: config.version,
      }

      console.log(`** Initialization Result **\n`)
      printTable({ data: resultInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
