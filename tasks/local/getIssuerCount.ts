import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from '../common/tools'
// npx hardhat getIssuerCount --network localFin
wrappedTask('getIssuerCount', 'Get list of issuers', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const result = await contract.getIssuerCount()
      console.log('=====result', Number(result))
    } catch (error) {
      showErrorDetails({ error })
    }
  },
)
