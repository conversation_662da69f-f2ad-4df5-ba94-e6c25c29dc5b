import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from '../common/tools'
import { convertToHex } from '@tasks/common/convertToHex'

// npx hardhat issuerHasAccount --network localFin --account-id 302 --issuer-id 2221
wrappedTask('issuerHasAccount', 'Check if an account can mint.', { filePath: path.basename(__filename) })
  .addOptionalParam('accountId', 'issuer id')
  .addOptionalParam('issuerId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    console.log(`** checkMint Parameters **\n`)
    const params = {
      issuerId,
      accountId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })
      const receipt = await contract.hasAccount(issuerId, accountId)

      console.log(`** checkMint receipt Information **\n`)
      printTable({ data: receipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
