import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { BusinessZoneAccount } from '@/types'
// npx hardhat listenEvent --network localFin
wrappedTask('listenEvent', 'register ibc app address to contract manager', {
  filePath: path.basename(__filename),
}).setAction(async (taskArgs, hre) => {
  const { contract } = await getContractWithSigner<BusinessZoneAccount>({ hre, contractName: 'BusinessZoneAccount' })
  await contract.addListener('SyncBusinessZoneStatus', (from, to, value, event) => {
    console.log('Event received:', event)
    console.log('From:', from)
    console.log('To:', to)
    console.log('Value:', value.toString())
  })
  console.log('Listening for events... Press Ctrl+C to stop.')
  await new Promise(() => {})
})
