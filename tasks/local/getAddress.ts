import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from '../common/tools'
// npx hardhat getAddress --network localFin
wrappedTask('getAddress', 'Get list of issuers', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'AccountSyncBridge' })

      const result = await contract.getAccessCtrlAddress()
      console.log('=====result', result)
    } catch (error) {
      showErrorDetails({ error })
    }
  },
)
