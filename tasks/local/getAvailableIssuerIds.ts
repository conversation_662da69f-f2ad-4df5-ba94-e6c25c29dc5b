import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from '../common/tools'

// npx hardhat getAvailableIssuerIds --network localFin  --zone-id 3001
wrappedTask('getAvailableIssuerIds', 'Check if an account can mint.', { filePath: path.basename(__filename) })
  .addOptionalParam('zoneId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    const zoneId = Number(taskArguments.zoneId || '')
    console.log(`** checkMint Parameters **\n`)
    const params = {
      zoneId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })
      const receipt = await contract.getAvailableIssuerIds(zoneId)

      console.log(`** checkMint receipt Information **\n`)
      printTable({ data: receipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
