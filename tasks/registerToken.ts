import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('registerToken', 'register token', {
  filePath: path.basename(__filename),
})
  .addParam('provKey', 'provider key')
  .addParam('tokenId', 'token id')
  .addParam('provId', 'provider id')
  .addParam('tokenName', 'token name')
  .addParam('symbol', 'symbol')
  .setAction(async (taskArguments, hre) => {
    const tokenName = convertToHex({ hre, value: taskArguments.tokenName || '' })
    const tokenId = convertToHex({ hre, value: taskArguments.tokenId || '' })
    const providerId = convertToHex({ hre, value: taskArguments.provId || '' })
    const symbol = convertToHex({ hre, value: taskArguments.symbol || '' })
    const providerKey = taskArguments.provKey || ''

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })

    console.log(`*** Token登録: 0x${tokenId}`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(
      providerKey,
      ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256'],
      [providerId, tokenId, tokenName, symbol, deadline],
    )

    await executeReceipt(contract.addToken(providerId, tokenId, tokenName, symbol, traceId, deadline, sig[0]))
  })
