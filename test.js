const fs = require("fs");
const crypto = require("crypto");

const data = fs.readFileSync(
  `./scripts/backup-restore/backupfiles/localBiz/finaccounts.json`,
  "utf8"
);
const hash = crypto.createHash("md5").update(data).digest("hex");
const md5Obj = JSON.parse(
  fs.readFileSync(
    `./scripts/backup-restore/backupfiles/localBiz/md5.json`,
    "utf8"
  )
);

console.log("MD5 Hash:", hash);
const isValid = hash === md5Obj["finaccounts"];

console.log(`Is the MD5 valid? ${isValid}`);
