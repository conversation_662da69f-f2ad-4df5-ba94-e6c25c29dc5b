import { commonConfig, Network, networkConfig } from './helpers/constants'
import { delay } from './scenarios/utils'
import { AddBizZoneToIssuer } from './tasks/AddBizZoneToIssuer'
import { ApproveTask } from './tasks/ApproveTask'
import { BizTerminatingTask } from './tasks/BizTerminatingTask'
import { BurnTokenTask } from './tasks/BurnTokenTask'
import { ForceBurnTokenTask } from './tasks/ForceBurnTokenTask'
import { MintRenewableArgumentsTask } from './tasks/MintRenewableArgumentsTask'
import { MintTokenTask } from './tasks/MintTokenTask'
import { ModProviderTask } from './tasks/ModProviderTask'
import { RegisterAccTask } from './tasks/RegisterAccTask'
import { RegisterBizZoneTask } from './tasks/RegisterBizZoneTask'
import { RegisterEscrowAccTask } from './tasks/RegisterEscrowAccTask'
import { RegisterIssuerTask } from './tasks/RegisterIssuerTask'
import { RegisterProvTask } from './tasks/RegisterProvTask'
import { RegisterTokenTask } from './tasks/RegisterTokenTask'
import { RegisterValidTask } from './tasks/RegisterValidTask'
import { SetAccountStatusTask } from './tasks/SetAccountStatusTask'
import { SetActiveBusinessAccountWithZoneTask } from './tasks/SetActiveBusinessAccountWithZoneTask'
import { SetBizZoneTerminatedTask } from './tasks/SetBizZoneTerminatedTask'
import { SetTerminatedTask } from './tasks/SetTerminatedTask'
import { SetTokenEnabledTask } from './tasks/SetTokenEnabledTask'
import { SyncAccountTask } from './tasks/SyncAccountTask'
import { TransferSingleTask } from './tasks/TransferSingleTask'
import { TransferTask } from './tasks/TransferTask'
import { LIMIT_VALUES } from './scenarios/22_account_transaction_limit'
import { PartialForceBurnTokenTask } from './tasks/PartialForceBurnTokenTask'

/**
 * Main test suite for backup data preparation
 * Creates comprehensive test data across multiple networks with all possible variations
 */
describe('Backup Data Preparation', function () {
  this.timeout(0) // Disable timeout for long-running data preparation

  describe('Setup All Data Variations for Backup', function () {
    it('Should create comprehensive test data with all variations for backup', async function () {
      // Register provider for LocalFin network
      await new RegisterProvTask(Network.LocalFin).execute({
        provId: networkConfig[Network.LocalFin].PROV_ID,
      })

      // Modify provider with test name
      await new ModProviderTask(Network.LocalFin).execute({
        provName: 'test-provider',
      })

      // Register issuer for LocalFin network
      await new RegisterIssuerTask(Network.LocalFin).execute({
        issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
        bankCode: networkConfig[Network.LocalFin].BANK_CODE,
        issuerName: networkConfig[Network.LocalFin].ISSUER_NAME,
      })

      // Register validator for LocalFin network
      await new RegisterValidTask(Network.LocalFin).execute({
        validId: networkConfig[Network.LocalFin].VALID_ID,
        issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
        validName: networkConfig[Network.LocalFin].VALID_NAME,
      })

      // Register business zone
      await new RegisterBizZoneTask(Network.LocalFin).execute({
        zoneId: networkConfig[Network.LocalBiz].ZONE_ID,
        zoneName: networkConfig[Network.LocalBiz].ZONE_NAME,
      })

      // Add business zone to issuer
      await new AddBizZoneToIssuer(Network.LocalFin).execute({
        issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
        zoneId: networkConfig[Network.LocalBiz].ZONE_ID,
      })

      // Register token for LocalFin network
      await new RegisterTokenTask(Network.LocalFin).execute()

      // Register multiple accounts for LocalFin network
      const registerAccTask = new RegisterAccTask(Network.LocalFin)

      const accountsToRegister = [
        {
          id: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
          name: networkConfig[Network.LocalFin].ACCOUNT_NAME_1,
          limitValues: JSON.stringify(LIMIT_VALUES),
        },
        { id: networkConfig[Network.LocalFin].ACCOUNT_ID_2, name: networkConfig[Network.LocalFin].ACCOUNT_NAME_2 },
        { id: networkConfig[Network.LocalFin].ACCOUNT_ID_3, name: networkConfig[Network.LocalFin].ACCOUNT_NAME_3 },
        { id: networkConfig[Network.LocalFin].ACCOUNT_ID_4, name: networkConfig[Network.LocalFin].ACCOUNT_NAME_4 },
        { id: networkConfig[Network.LocalFin].ACCOUNT_ID_5, name: networkConfig[Network.LocalFin].ACCOUNT_NAME_5 },
      ]

      for (const account of accountsToRegister) {
        // Register individual account
        await registerAccTask.execute({
          accountId: account.id,
          accountName: account.name,
          limitValues: account.limitValues || undefined,
        })
      }

      // Register escrow account
      await new RegisterEscrowAccTask(Network.LocalFin).execute({
        dstZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
        escrowAccount: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
      })

      // Register provider for LocalBiz network
      await new RegisterProvTask(Network.LocalBiz).execute()

      // Register validator for LocalBiz network
      await new RegisterValidTask(Network.LocalBiz).execute()

      // Register token for LocalBiz network
      await new RegisterTokenTask(Network.LocalBiz).execute()
      // Mint renewable token task
      const mintRenewableTask = new MintRenewableArgumentsTask(Network.LocalBiz)

      // Mint first renewable token (unlocked)
      await mintRenewableTask.execute({
        tokenId: networkConfig[Network.LocalBiz].RENEWABLE_ID_1,
        metadataId: networkConfig[Network.LocalBiz].METADATA_ID,
        metadataHash: networkConfig[Network.LocalBiz].METADATA_HASH,
        isLocked: 'false',
      })

      // Mint second renewable token (locked)
      await mintRenewableTask.execute({
        tokenId: networkConfig[Network.LocalBiz].RENEWABLE_ID_2,
        metadataId: networkConfig[Network.LocalBiz].METADATA_ID,
        metadataHash: networkConfig[Network.LocalBiz].METADATA_HASH,
        isLocked: 'true',
      })

      // Sync accounts from LocalFin to LocalBiz network
      const syncAccountTaskBiz = new SyncAccountTask(Network.LocalBiz)

      for (const account of accountsToRegister) {
        // Sync individual account to business zone
        await syncAccountTaskBiz.execute({
          accountId: account.id,
          accountName: account.name,
        })
      }

      // Wait for synchronization to complete
      await delay()

      // Set approval token
      await new ApproveTask(Network.LocalBiz).execute({
        ownerId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        spenderId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
      })

      // Activate business accounts task
      const setActiveTask = new SetActiveBusinessAccountWithZoneTask(Network.LocalFin)

      const accountsToActivate = [
        networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        networkConfig[Network.LocalFin].ACCOUNT_ID_3,
        networkConfig[Network.LocalFin].ACCOUNT_ID_4,
        networkConfig[Network.LocalFin].ACCOUNT_ID_5,
      ]

      for (const accountId of accountsToActivate) {
        // Activate individual business account
        await setActiveTask.execute({ accountId })
      }

      // Mint tokens
      await new MintTokenTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
        amount: '3000',
      })

      // Charge tokens to Biz zone
      await new TransferTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
        toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
        amount: '800',
      })

      // Wait for charge to complete
      await delay()

      // Mint tokens
      await new MintTokenTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        amount: '5000',
      })

      // Burn tokens
      await new BurnTokenTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        amount: '100',
      })

      // Charge tokens to Biz zone
      await new TransferTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
        amount: '1000',
      })

      // Wait for charge to complete
      await delay()

      // Discharge tokens from Biz zone
      await new TransferTask(Network.LocalBiz).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
        amount: '500',
      })

      // Wait for discharge to complete
      await delay()

      // Transfer tokens between accounts within Fin zone
      await new TransferSingleTask(Network.LocalFin).execute({
        sendAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        fromAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
        toAccountId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
        amount: '100',
      })

      // Freeze account to test frozen status
      await new SetAccountStatusTask(Network.LocalFin).execute({
        accountStatus: commonConfig.STATUS_FROZEN,
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
      })

      // Force burn tokens from frozen account
      await new ForceBurnTokenTask(Network.LocalFin).execute({
        issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
      })

      // Terminate business zone accounts
      const bizTerminatingTask = new BizTerminatingTask(Network.LocalBiz)

      const accountsToTerminate = [
        networkConfig[Network.LocalFin].ACCOUNT_ID_3,
        networkConfig[Network.LocalFin].ACCOUNT_ID_4,
      ]

      for (const accountId of accountsToTerminate) {
        // Terminate individual business account
        await bizTerminatingTask.execute({ accountId })
      }

      // Wait for termination to complete
      await delay()

      // Freeze account
      await new SetAccountStatusTask(Network.LocalFin).execute({
        accountStatus: commonConfig.STATUS_FROZEN,
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
      })

      // Partial force burn account
      await new PartialForceBurnTokenTask(Network.LocalFin).execute({
        issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
        burnedAmount: '50',
        burnedBalance: '50',
      })

      // Set business zone terminated
      await new SetBizZoneTerminatedTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_4,
        zoneId: networkConfig[Network.LocalBiz].ZONE_ID,
      })

      // Set account terminated
      await new SetTerminatedTask(Network.LocalFin).execute({
        accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_4,
        reasonCode: 'test_termination',
      })

      // Disable token
      await new SetTokenEnabledTask(Network.LocalFin).execute({
        tokenId: networkConfig[Network.LocalFin].TOKEN_ID,
        enabled: 'false',
      })
    })
  })
})
