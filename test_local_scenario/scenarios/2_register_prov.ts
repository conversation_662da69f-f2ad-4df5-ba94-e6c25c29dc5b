import { Network } from '../helpers/constants'
import { ModProviderArguments, RegisterProvArguments } from '../helpers/task-arguments'
import { RegisterProvTask } from '../tasks/RegisterProvTask'
import { ModProviderTask } from '../tasks/ModProviderTask'

async function registerProv(network: Network, args: Partial<RegisterProvArguments> = {}) {
  const registerProvTask = new RegisterProvTask(network)
  return await registerProvTask.execute(args)
}

async function modProvider(network: Network, args: Partial<ModProviderArguments> = {}) {
  const modProviderTask = new ModProviderTask(network)
  return await modProviderTask.execute(args)
}

export { registerProv, modProvider }
