import '@nomicfoundation/hardhat-chai-matchers'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance } from '@test/common/types'
import { assert } from 'chai'

describe('version()', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance

  const setupFixture = async () => {
    ;({ balanceSyncBridge } = await contractFixture<BalanceSyncBridgeContractType>())
  }

  beforeEach(async () => {
    await setupFixture()
  })

  describe('正常系', () => {
    describe('初期状態', () => {
      it('バージョン情報を取得できること', async () => {
        const version = await balanceSyncBridge.version()
        assert.equal(version, BASE.APP.VERSION, 'version')
      })
    })
  })
})
