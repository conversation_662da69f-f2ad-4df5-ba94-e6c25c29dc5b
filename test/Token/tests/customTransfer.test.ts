import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('customTransfer()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let transferProxy: TransferProxyInstance
  let customTransfer1: TransferableMock1Instance
  let customTransfer2: TransferableMock2Instance
  let customTransfer3: TransferableMock3Instance

  describe('準正常系', () => {
    // This test support to coverage TokenLib_transfer
    before(async () => {
      ;({
        accounts,
        provider,
        issuer,
        validator,
        token,
        transferProxy,
        customTransfer1,
        customTransfer2,
        customTransfer3,
      } = await contractFixture<TokenContractType>())
    })

    describe('getZone return some error', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const account of [
          { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT3.ID, accountName: BASE.ACCOUNT.ACCOUNT3.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT10.ID, accountName: BASE.ACCOUNT.ACCOUNT10.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT11.ID, accountName: BASE.ACCOUNT.ACCOUNT11.NAME },
        ]) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              accountId: account.accountId,
              accountName: account.accountName,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer1.getAddress(),
          position: 0,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer2.getAddress(),
          position: 1,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer3.getAddress(),
          position: 2,
        })
        await tokenFuncs.approve({
          token,
          accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      // TODO: 後で修正
      // it('should revert when getZone return error provider not exist', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.EMPTY.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.ID0,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(
      //     token.customTransfer(
      //       BASE.ACCOUNT.ACCOUNT0.ID,
      //       BASE.ACCOUNT.ACCOUNT1.ID,
      //       BASE.ACCOUNT.ACCOUNT0.ID,
      //       100,
      //       utils.toBytes32(''),
      //       utils.toBytes32(''),
      //       '',
      //       BASE.TRACE_ID,
      //       { from: accounts[0] },
      //     ),
      //     ERR.PROV.PROV_NOT_EXIST,
      //   );
      // });

      // it('should revert when getZone return error zoneId is 0', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.PROV0.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.EMPTY_ID,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(
      //     token.customTransfer(
      //       BASE.ACCOUNT.ACCOUNT0.ID,
      //       BASE.ACCOUNT.ACCOUNT1.ID,
      //       BASE.ACCOUNT.ACCOUNT0.ID,
      //       100,
      //       utils.toBytes32(''),
      //       utils.toBytes32(''),
      //       '',
      //       BASE.TRACE_ID,
      //       { from: accounts[0] },
      //     ),
      //     ERR.PROV.ZONE_NOT_EXIST,
      //   );
      // });
    })
  })
})
