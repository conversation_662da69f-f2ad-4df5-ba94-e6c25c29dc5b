import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('hasToken()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
    })

    describe('tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
      })

      it('未登録tokenIdを指定した場合、エラーが返されること', async () => {
        const tokenId = utils.toBytes32('x5555')

        const result = await tokenFuncs.hasToken({ token, prams: [tokenId, false] })

        utils.assertEqualForEachField(result, { success: false, err: ERR.TOKEN.TOKEN_ID_NOT_EXIST })
      })

      it('tokenが存在する場合、trueが取得できること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID

        const result = await tokenFuncs.hasToken({ token, prams: [tokenId, true] })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('空tokenIdを指定した場合、エラーが返されること', async () => {
        const tokenId = utils.toBytes32('')

        const result = await tokenFuncs.hasToken({ token, prams: [tokenId, true] })

        utils.assertEqualForEachField(result, { success: false, err: ERR.TOKEN.TOKEN_INVALID_VAL })
      })
    })

    describe('tokenが無効状態の場合', () => {
      before(async () => {
        await tokenFuncs.setTokenEnabled({ token, accounts, enabled: false })
      })

      it('有効状態チェックフラグがfalseの場合、trueが取得できること', async () => {
        const result = await tokenFuncs.hasToken({ token, prams: [BASE.TOKEN.TOKEN1.ID, false] })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('有効状態チェックフラグがtrueの場合、、エラーが返されること', async () => {
        const result = await tokenFuncs.hasToken({ token, prams: [BASE.TOKEN.TOKEN1.ID, true] })

        utils.assertEqualForEachField(result, { success: false, err: ERR.TOKEN.TOKEN_DISABLED })
      })
    })
  })
})
