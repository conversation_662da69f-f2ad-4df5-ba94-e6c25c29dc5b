import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

const valid0 = { eoaKey: 1, ...BASE.VALID.VALID0 }
const valid1 = { eoaKey: 2, ...BASE.VALID.VALID1 }

describe('checkApprove()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts: any[] = Object.values(BASE.ACCOUNT).slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })
      it('Approveが可能かどうか確認できること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: true,
          err: '',
        }
        utils.assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts: any[] = Object.values(BASE.ACCOUNT).slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT3.ID },
        })
      })

      it('存在しないバリデータを指定した場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID10.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('OwnerAccountのステータスがアクティブではない場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT3.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_DISABLED,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('SenderAccountのステータスがアクティブではない場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT3.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_DISABLED,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('バリデータに紐づいていないアカウントの場合、エラーが返却されること', async () => {
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER10.ID,
            bankCode: BASE.ISSUER.ISSUER10.BANK_CODE,
          },
        })
        await issuerFuncs.addIssuerRole({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER10.ID,
          },
        })
        await validatorFuncs.addValidator({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID20.ID,
            issuerId: BASE.ISSUER.ISSUER10.ID,
          },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID20.ID,
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
            accountName: BASE.ACCOUNT.ACCOUNT10.NAME,
          },
        })

        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT10.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('バリデータ署名が不正である場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid1.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.VALID.VALIDATOR_NOT_ROLE,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('バリデータ署名が有効期限切れである場合、エラーが返却されること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
            deadline: exceededDeadline,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACTRL.ACTRL_SIG_TIMEOUT,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('アカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const invalidAccountSig = privateKey.sig(
          siginfo.signer,
          ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
          [
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
            10,
            BASE.ACCOUNT_SIG_MSG.TRANSFER,
          ],
        )[0]

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
            accountSignature: invalidAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('空のアカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const emptyAccountSig = utils.toBytes('')

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
            accountSignature: emptyAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('should revert when account is not registered', async () => {
        const amount = ****************
        const pt = await utils.getDeadline()
        const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await tokenFuncs.checkApprove({
          token,
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: BASE.ACCOUNT.ACCOUNT1.ID,
          spenderId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: amount,
          sigInfo: siginfo,
          options: {
            privateKeyForSig: privateKey.key[valid0.eoaKey],
            eoaKey: valid0.eoaKey,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_EXCEED_APPROVAL_LIMIT,
        }
        utils.assertEqualForEachField(result, expected)
      })
    })
  })
})
