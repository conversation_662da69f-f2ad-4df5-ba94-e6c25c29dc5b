import privateKey from '@/privateKey'
import { accountFuncs } from '@/test/Account/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = utils.toBytes32('x490')

// This MAX_UINT256 is used to test overflow handling
// Hex: 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
const MAX_UINT256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935n

describe('burnCancel()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('burnがされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await validatorFuncs.addAccount({ validator, accounts })
        await tokenFuncs.mint({ token, accounts, amount: 100 })
        await tokenFuncs.burn({ token, accounts, amount: 100 })
      })

      it('BurnCancelされた額が加算されること、totalSupplyが加算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        const burnCancelTx = await tokenFuncs.burnCancel({
          token,
          accounts,
          amount: amount,
          blockTimestamp: blockTimestamp,
        })

        await expect(burnCancelTx)
          .emit(token, 'BurnCancel')
          .withArgs(
            BASE.ZONE_ID.ID0,
            validatorId,
            issuerId,
            accountId,
            accountName,
            amount,
            100n,
            blockTimestamp,
            BASE.TRACE_ID,
          )
        const accountData = await accountFuncs.getAccount({ account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 100 })
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 100 })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('issuerRoleが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: { issuerId, accountId },
          }),
        ).revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })

    describe('burnがされている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        await validatorFuncs.addValidator({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            validatorId: BASE.VALID.VALID1.ID,
          },
        })
        for (const _issuer of [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1]) {
          await issuerFuncs.addIssuerRole({ issuer, accounts, options: { issuerId: _issuer.ID } })
        }
        await tokenFuncs.mint({ token, accounts, amount: 100 })
        await tokenFuncs.burn({ token, accounts, amount: 100 })
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.EMPTY.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: { accountId },
          }),
        ).revertedWith(ERR.TOKEN.TOKEN_INVALID_VAL)
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const issuerId = BASE.ISSUER.EMPTY.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: { issuerId, accountId },
          }),
        ).revertedWith(ERR.TOKEN.TOKEN_INVALID_VAL)
      })

      it('未登録accountIdを指定した場合、エラーがスローされること', async () => {
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: 100,
            blockTimestamp: blockTimestamp,
            options: { accountId: BASE.ACCOUNT.ACCOUNT15.ID },
          }),
        ).revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('issuerに紐付けられていないaccountIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT5.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: { accountId },
          }),
        ).revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: {
              accountId,
              sig: ['0x1234', ''],
            },
          }),
        ).revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()
        const exceededDeadline = await utils.getExceededDeadline()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: {
              accountId,
              deadline: exceededDeadline,
            },
          }),
        ).revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })
    })

    describe('accountが解約状態の場合', () => {
      before(async () => {
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
        await validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
      })

      it('解約済accountIdを指定した場合、エラーがスローされること', async () => {
        const accountId = TERMINATED_ACCOUNT1
        const amount = 100
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        await expect(
          tokenFuncs.burnCancel({
            token,
            accounts,
            amount: amount,
            blockTimestamp: blockTimestamp,
            options: { accountId },
          }),
        ).revertedWith(ERR.ACCOUNT.ACCOUNT_TERMINATED)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('getZone return some error', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await tokenFuncs.mint({ token, accounts, amount: 200 })
        await tokenFuncs.burn({ token, accounts, amount: 100 })
      })

      it('should revert when totalSupply is overflow', async () => {
        // amount is never negative, use MAX_UINT256+1 to get overflow case
        // This revert will return "Arithmetic operation overflowed" instead of our message
        const blockTimestamp = await utils.getLatestBlockTimestamp()

        const eoaKey = BASE.EOA.ISSUER1
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID

        const _deadline = await utils.getDeadline()
        const signer = privateKey.key[eoaKey]
        const _sig = privateKey.sig(
          signer,
          ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
          [issuerId, accountId, MAX_UINT256, blockTimestamp, _deadline],
        )

        await expect(
          token
            .connect(accounts[0])
            .burnCancel(issuerId, accountId, MAX_UINT256, blockTimestamp, BASE.TRACE_ID, _deadline, _sig[0]),
        ).reverted
      })

      // TODO: 後で修正
      // it('should revert when getZone return error provider not exist', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.EMPTY.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.ID0,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;
      //   const blockTimestamp = await utils.getLatestBlockTimestamp();

      //   await truffleAssert.reverts(
      //     tokenFuncs.burnCancel(token, accounts, amount, blockTimestamp),
      //     ERR.PROV.PROV_NOT_EXIST,
      //   );
      // });

      // it('should revert when getZone return error zoneId is 0', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.PROV0.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.EMPTY_ID,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;
      //   const blockTimestamp = await utils.getLatestBlockTimestamp();

      //   await truffleAssert.reverts(
      //     tokenFuncs.burnCancel(token, accounts, amount, blockTimestamp),
      //     ERR.PROV.ZONE_NOT_EXIST,
      //   );
      // });
    })
  })
})
