import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ROLLED_EOA_KEY } from '@test/AccessCtrl/helpers/constant'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('delAdminRole()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
  }

  beforeEach(async () => {
    await accessCtrlFuncs.addAdminRole({
      accessCtrl,
      from: accounts[9],
      account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
    })
  })

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      it('Admin権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと', async () => {
        const tx = await accessCtrlFuncs.delAdminRole({
          accessCtrl,
          accounts,
          account: await accounts[9].getAddress(),
        })

        await expect(tx).to.not.emit(accessCtrl, 'RoleRevoked')
      })

      it('権限が削除されること', async () => {
        const tx = await accessCtrlFuncs.delAdminRole({
          accessCtrl,
          accounts,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })

        const expectParams = {
          role: BASE.ROLE.ADMIN,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
          sender: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleRevoked')
          .withArgs(...Object.values(expectParams))
        assert.notOk(
          await accessCtrlFuncs.hasRole({
            accessCtrl,
            role: BASE.ROLE.ADMIN,
            account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
          }),
        )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      it('権限を削除するアカウントがDEFAULT_ADMIN権限の場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.delAdminRole({
          accessCtrl,
          accounts,
          account: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.delAdminRole({
          accessCtrl,
          accounts,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
          options: { sender: accounts[ROLLED_EOA_KEY.NOT_ADMIN] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
