import { castReturnType } from '@test/common/utils'
import { FunctionType } from './types'

/**
 * accountのイベントを呼ぶ関数を持つobject
 * TODO: accountFuncについても他のファイルと同様にsend functionを含めて作成する
 */
export const accountFuncs: FunctionType = {
  version: ({ account }) => {
    return castReturnType(account.version())
  },
  hasAccount: ({ account, params }) => {
    return castReturnType(account.hasAccount(...params))
  },
  isTerminated: ({ account, params }) => {
    return castReturnType(account.isTerminated(...params))
  },
  balanceOf: ({ account, params }) => {
    return castReturnType(account.balanceOf(...params))
  },
  getAccountId: ({ account, params }) => {
    return castReturnType(account.getAccountId(...params))
  },
  getAccount: async ({ account, params }) => {
    return castReturnType(account.getAccount(...params))
  },
  getDestinationAccount: ({ account, params }) => {
    return castReturnType(account.getDestinationAccount(...params))
  },
  getAccountCount: ({ account, params = [] }) => {
    return castReturnType(account.getAccountCount(...params))
  },
  getValidatorIdByAccountId: ({ account, params }) => {
    return castReturnType(account.getValidatorIdByAccountId(...params))
  },
  // approve: async ({ account, accounts, spenderId, amount, option = {} }: ApproveType) => {
  //   const { from, ownerId = BASE.ACCOUNT.ACCOUNT1.ID } = option
  //   const _from = from ?? accounts[0]
  //   return account.connect(_from).approve(ownerId, spenderId, amount)
  // },
  // getAccountsAll: async ({ account, offset }: GetAccountsAllType) => {
  //   // TODO: Fix correct input for getAccountsAll
  //   return account.getAccountsAll(offset) as unknown as Promise<EventReturnType['Account']['GetAccountsAll']>
  // },
  // // DCPF-27357 Function setAccountsAll already commented in Account contract
  // // setAccountsAll: async (
  // //   account: AccountInstance,
  // //   params: any,
  // //   {
  // //     sig,
  // //     deadline,
  // //     eoaKey = BASE.EOA.ADMIN,
  // //     hash = BASE.SALTS.SET_ACCOUNTS_ALL,
  // //   }: Partial<SetValidatorsAllOption & ContractCallOption> = {},
  // // ) => {
  // //   const _deadline = deadline ?? (await utils.getDeadline());
  // //   const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline]);

  // //   return account.setAccountsAll(params, _deadline, _sig[0]);
  // // },
}
