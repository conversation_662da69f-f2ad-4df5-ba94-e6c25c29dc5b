import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountContractType } from '@test/Account/helpers/types'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('partialForceBurn()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance
  let fakeIssuer: SignerWithAddress

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager, businessZoneAccount } =
      await contractFixture<AccountContractType>())
  }

  describe('正常系', () => {
    let ibcAddress

    before(async () => {
      await setupFixture()

      await helpers.impersonateAccount(await issuer.getAddress())
      await helpers.setBalance(await issuer.getAddress(), 100n ** 18n)
      fakeIssuer = await ethers.getSigner(await issuer.getAddress())
      ibcAddress = accounts[0]
    })

    describe('fin Account残高が300で、biz Account残高が150の場合', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()

        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })

        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })

        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })

        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })

        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })

        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 150,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('fin Account残高が指定したburnedAmountより大きい場合、fin Account残高のみが更新されること', async () => {
        const beforeBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(beforeBurn.accountDataAll.balance).to.equal('300')
        expect(beforeBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)
        expect(beforeBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')

        const tx = await account.connect(fakeIssuer).partialForceBurn(BASE.ACCOUNT.ACCOUNT1.ID, 100, 200, BASE.TRACE_ID)

        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID, 100, 200, [])

        const afterBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(afterBurn.accountDataAll.balance).to.equal('200')
        expect(afterBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')
      })

      it('fin Account残高が指定したburnedAmountより小さく、total残高が大きい場合、biz Account残高が全て消却されること', async () => {
        const beforeBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(beforeBurn.accountDataAll.balance).to.equal('200')
        expect(beforeBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')

        const tx = await account.connect(fakeIssuer).partialForceBurn(BASE.ACCOUNT.ACCOUNT1.ID, 250, 100, BASE.TRACE_ID)

        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID, 250, 100, (forceDischarge) => {
            const data = forceDischarge.map((data) => ({
              zoneId: data[0],
              dischargeAmount: data[1],
            }))
            expect(data).to.deep.equal([
              {
                zoneId: BASE.ZONE_ID.ID1,
                dischargeAmount: 150,
              },
            ])
            return true
          })

        const afterBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(afterBurn.accountDataAll.balance).to.equal('100')
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('0')
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].accountStatus).to.equal(BASE.STATUS.FORCE_BURNED)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })

        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })

        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('Issuerコントラクト以外から呼び出した場合、エラーがスローされること', async () => {
        const result = account.partialForceBurn(BASE.ISSUER.ISSUER1.ID, BASE.ACCOUNT.ACCOUNT1.ID, 100, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })

      it('accountが存在しない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.EMPTY.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('burnedAmountが残高より大きい場合、ACCOUNT_INVALID_BURNED_AMOUNTエラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 1000,
            burnedBalance: 0,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_AMOUNT)
      })

      it('burnedBalanceが実際の残高と一致しない場合、ACCOUNT_INVALID_BURNED_BALANCEエラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 100,
            burnedBalance: 150,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_BALANCE)
      })

      it('Accountが凍結状態でない場合、エラーがスローされること', async () => {
        // アカウントをアクティブ状態に設定
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_FROZEN)
      })
    })
  })
})
