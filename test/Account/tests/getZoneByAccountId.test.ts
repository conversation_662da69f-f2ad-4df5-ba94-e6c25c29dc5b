import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { AccountContractType } from '@/test/Account/helpers/types'
import { businessZoneAccountFuncs } from '@/test/BusinessZoneAccount/helpers/function'
import { contractFixture } from '@/test/common/contractFixture'
import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'

describe('getZoneByAccountId()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, contractManager, businessZoneAccount } =
      await contractFixture<AccountContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態', () => {
      let ibcAddress

      before(async () => {
        ibcAddress = await accounts[0]
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('zone情報のリストが取得できること', async () => {
        const expectedZoneInfo = [
          { zoneId: BASE.ZONE_ID.ID1, zoneName: BASE.ZONE_NAME.NAME1 },
          { zoneId: BASE.ZONE_ID.ID2, zoneName: BASE.ZONE_NAME.NAME2 },
        ]

        const result = await validatorFuncs.getZoneByAccountId({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expectedZoneInfo.forEach((v, i) => {
          assertEqualForEachField(result.zones[i], {
            zoneId: v.zoneId,
            zoneName: v.zoneName,
          })
        })
      })
    })
  })
})
