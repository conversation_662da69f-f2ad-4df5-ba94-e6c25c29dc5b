import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountContractType } from '@test/Account/helpers/types'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getJSTDay, getLatestBlockTimestamp } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('forceBurn()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance
  let ibcAddress
  let fakeIssuer

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager, businessZoneAccount } =
      await contractFixture<AccountContractType>())
    ibcAddress = await accounts[0]
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 600,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })

        await await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 100,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('Accountの残高を強制償却できること,totalSupplyが減算されること', async () => {
        await helpers.impersonateAccount(await issuer.getAddress())
        await helpers.setBalance(await issuer.getAddress(), 100n ** 18n)
        fakeIssuer = await ethers.getSigner(await issuer.getAddress())
        const tx = await account.connect(fakeIssuer).forceBurn(BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID)

        const expectParams = {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          traceId: BASE.TRACE_ID,
          burnedAmount: 600,
          burnedBalance: 0,
          forceDischarge: [
            {
              zoneId: BASE.ZONE_ID.ID1,
              dischargeAmount: 300,
            },
            {
              zoneId: BASE.ZONE_ID.ID2,
              dischargeAmount: 100,
            },
          ],
        }

        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(
            expectParams.validatorId,
            expectParams.accountId,
            expectParams.traceId,
            expectParams.burnedAmount,
            expectParams.burnedBalance,
            (forceDischarge) => {
              const data = forceDischarge.map((data) => ({
                zoneId: data[0],
                dischargeAmount: data[1],
              }))

              expect(data).to.deep.equal(expectParams.forceDischarge)
              return true
            },
          )

        const result = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const finExpect = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.FORCE_BURNED,
          balance: '0',
          reasonCode: BASE.REASON_CODE1,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          appliedAt: '0',
          registeredAt: await getLatestBlockTimestamp(),
          terminatingAt: '0',
          terminatedAt: '0',
          mintLimit: '3000',
          burnLimit: '4000',
          chargeLimit: '2000',
          dischargeLimit: '4000',
          transferLimit: '1000',
          cumulativeLimit: '5000',
          cumulativeAmount: '1000',
          cumulativeDate: String(await getJSTDay()),
        }

        const cumulativeTransactionLimitsExpect = {
          cumulativeMintLimit: '1000',
          cumulativeMintAmount: '600',
          cumulativeBurnLimit: '1000',
          cumulativeBurnAmount: '0',
          cumulativeChargeLimit: '1000',
          cumulativeChargeAmount: '400',
          cumulativeDischargeLimit: '1000',
          cumulativeDischargeAmount: '0',
          cumulativeTransferLimit: '800',
          cumulativeTransferAmount: '0',
        }

        const bizExpect = [
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
            balance: '0',
            accountStatus: BASE.STATUS.FORCE_BURNED,
            appliedAt: await getLatestBlockTimestamp(),
            registeredAt: await getLatestBlockTimestamp(),
            terminatingAt: '0',
            terminatedAt: '0',
          },
        ]

        assertEqualForEachField(result.accountDataAll, finExpect)
        assertEqualForEachField(result.accountDataAll.cumulativeTransactionLimits, cumulativeTransactionLimitsExpect)
        assertEqualForEachField(
          result.accountDataAll.businessZoneAccounts.map((v) => {
            return {
              accountName: v.accountName,
              zoneId: v.zoneId,
              zoneName: v.zoneName,
              balance: v.balance,
              accountStatus: v.accountStatus,
              appliedAt: v.appliedAt,
              registeredAt: v.registeredAt,
              terminatingAt: v.terminatingAt,
              terminatedAt: v.terminatedAt,
            }
          }),
          bizExpect.map((v) => v),
        )
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        assertEqualForEachField(totalSupply, { totalSupply: 900 - 600 })
        assert.equal(result.err, '')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('Issuerコントラクト以外から呼び出した場合、エラーがスローされること', async () => {
        await expect(account.forceBurn(BASE.ISSUER.ISSUER1.ID, BASE.ACCOUNT.ACCOUNT1.ID)).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })

      it('accountが存在しない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.forceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('Accountが凍結状態でない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.forceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_FROZEN)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.forceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.EMPTY.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })
    })

    describe('account is not registered or frozen', () => {
      before(async () => {
        await helpers.impersonateAccount(await issuer.getAddress())
        await helpers.setBalance(await issuer.getAddress(), 100n ** 18n)
        fakeIssuer = await ethers.getSigner(await issuer.getAddress())
      })

      it('should revert accounId not exist when check hasAccount in isFrozen', async () => {
        // Hack: Fake issuer can call to forceBurn
        // UNREACHABLE false case: This test only for coverage, not for real case
        const result = account.connect(fakeIssuer).forceBurn(BASE.ACCOUNT.ACCOUNT5.ID, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('should revert accounId not valid when check hasAccount in isFrozen', async () => {
        // Hack: Fake issuer can call to forceBurn
        // UNREACHABLE false case: This test only for coverage, not for real case
        const result = account.connect(fakeIssuer).forceBurn(BASE.ACCOUNT.EMPTY.ID, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('should revert account not frozen when check isFrozen in forceBurn', async () => {
        const result = issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.TERMINATED,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })
})
