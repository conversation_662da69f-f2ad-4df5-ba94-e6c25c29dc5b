import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let provider: ProviderInstance

  describe('正常系', () => {
    before(async () => {
      ;({ provider } = await contractFixture<ProviderContractType>())
    })

    it('should revert when initialized', async () => {
      const result = provider.initialize(await provider.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
