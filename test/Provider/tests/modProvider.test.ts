import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modProvider()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
      })

      it('providerが更新できること', async () => {
        const providerId = BASE.PROV.PROV0.ID
        const newProvName = BASE.PROV.PROV0.NAME

        let tx = await providerFuncs.modProvider({ provider, accounts, providerName: newProvName })

        await expect(tx).to.emit(provider, 'ModProvider').withArgs(providerId, newProvName, anyValue)

        let result = await providerFuncs.getProvider({ provider })
        assertEqualForEachField(result, {
          providerId: BASE.PROV.PROV0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          err: '',
        })

        // プロバイダー名、zoneIDを指定しないときは元の値のまま
        tx = await providerFuncs.modProvider({
          provider,
          accounts,
          providerName: BASE.PROV.EMPTY.NAME,
        })

        await expect(tx).to.emit(provider, 'ModProvider').withArgs(providerId, BASE.PROV.EMPTY.NAME, anyValue)

        result = await providerFuncs.getProvider({ provider })
        assertEqualForEachField(result, {
          providerId: BASE.PROV.PROV0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          err: '',
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
      })

      it('Admin権限ではない署名の場合、エラーがスローされること', async () => {
        const result = providerFuncs.modProvider({
          provider,
          accounts,
          providerName: BASE.PROV.PROV0.NAME,
          options: { eoaKey: BASE.EOA.PROV1 },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_ADMIN_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = providerFuncs.modProvider({
          provider,
          accounts,
          providerName: BASE.PROV.PROV0.NAME,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('未登録のproviderId場合、エラーがスローされること', async () => {
        const result = providerFuncs.modProvider({
          provider,
          accounts,
          providerName: BASE.PROV.PROV0.NAME,
          options: {
            providerId: BASE.PROV.PROV1.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_ID_NOT_EXIST)
      })
    })
  })
})
