import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddTokenOption, EventReturnType, ProviderInstance, TokenInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline, toBytes32 } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import _ from 'lodash'
import { before } from 'mocha'
import { Diff, Intersection } from 'utility-types'

describe('addToken()', () => {
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider, roleが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
      })

      it('tokenが追加できること', async () => {
        const params: AddTokenOption = {
          tokenId: BASE.TOKEN.TOKEN1.ID,
          providerId: BASE.PROV.PROV0.ID,
          name: BASE.TOKEN.TOKEN1.NAME,
          symbol: BASE.TOKEN.TOKEN1.SYMBOL,
        }

        const tx = await providerFuncs.addToken({ provider, accounts, options: params })

        await expect(tx).to.emit(provider, 'AddTokenByProvider').withArgs(params.providerId, params.tokenId, anyValue)

        await expect(tx).to.emit(token, 'AddToken').withArgs(params.tokenId, BASE.ZONE_ID.ID0, anyValue, true, anyValue)

        const result = await providerFuncs.getToken({ provider, options: [params.providerId] })
        const expectedResult = _.omit<
          typeof params,
          keyof Diff<typeof params, EventReturnType['Provider']['GetToken']>
        >(params, ['providerId'])
        assertEqualForEachField(result, expectedResult)

        const resultProv = await providerFuncs.getProvider({ provider })
        const expectedResultProv = _.pick<
          typeof params,
          keyof Intersection<typeof params, EventReturnType['Provider']['GetProvider']>
        >(params, ['providerId'])
        assertEqualForEachField(resultProv, expectedResultProv)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider, roleが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
      })

      it('providerIdが違う場合、エラーがスローされること', async () => {
        const result = providerFuncs.addToken({
          provider,
          accounts,
          options: { providerId: BASE.PROV.PROV1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.NOT_PROVIDER_ID)
      })

      it('無効署名の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addToken({ provider, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('タイムアウトが発生する場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = providerFuncs.addToken({ provider, accounts, options: { deadline: exceededDeadline } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })
    })

    describe('tokenが登録されている状態', () => {
      const params: AddTokenOption = {
        tokenId: BASE.TOKEN.TOKEN1.ID,
        providerId: BASE.PROV.PROV0.ID,
        name: BASE.TOKEN.TOKEN1.NAME,
        symbol: BASE.TOKEN.TOKEN1.SYMBOL,
      }

      before(async () => {
        await providerFuncs.addToken({ provider, accounts, options: params })
      })

      it('token重複登録の場合、エラーがスローされること', async () => {
        const newParams: AddTokenOption = {
          ...params,
          tokenId: BASE.TOKEN.TOKEN2.ID,
          name: BASE.TOKEN.TOKEN2.NAME,
          symbol: BASE.TOKEN.TOKEN2.SYMBOL,
        }

        const result = providerFuncs.addToken({ provider, accounts, options: newParams })
        await expect(result).to.be.revertedWith(ERR.TOKEN.TOKEN_ID_EXIST)
      })
    })
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider not role', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { providerId: toBytes32('0x103') },
        })
      })

      it('should revert when provider not role', async () => {
        const result = providerFuncs.addToken({
          provider,
          accounts,
          options: { providerId: toBytes32('0x103') },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_ROLE)
      })
    })
  })
})
