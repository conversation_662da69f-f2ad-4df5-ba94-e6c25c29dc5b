import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

describe('hasProvider()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
      })

      it('有効性確認フラグがtrueの場合、trueが返されること', async () => {
        const result = await providerFuncs.hasProvider({ provider, options: [BASE.PROV.PROV0.ID] })

        assertEqualForEachField(result, { success: true, err: '' })
      })

      it('有効性確認フラグがfalseの場合、trueが返されること', async () => {
        const result = await providerFuncs.hasProvider({ provider, options: [BASE.PROV.PROV0.ID] })

        assertEqualForEachField(result, { success: true, err: '' })
      })

      it('空providerId指定の場合、falseが返されること', async () => {
        const result = await providerFuncs.hasProvider({ provider, options: [BASE.PROV.EMPTY.ID] })

        assertEqualForEachField(result, { success: false, err: ERR.PROV.PROV_INVALID_VAL })
      })

      it('未登録providerId指定の場合、falseが返されること', async () => {
        const result = await providerFuncs.hasProvider({ provider, options: [BASE.PROV.PROV1.ID] })

        assertEqualForEachField(result, { success: false, err: ERR.PROV.PROV_ID_NOT_EXIST })
      })
    })
  })
})
