import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddProviderOption, ProviderInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addProvider()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('providerが登録できること', async () => {
        const params: AddProviderOption = {
          providerId: BASE.PROV.PROV0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
        }

        const tx = await providerFuncs.addProvider({ provider, accounts, options: params })

        await expect(tx)
          .to.emit(provider, 'AddProvider')
          .withArgs(params.providerId, params.zoneId, params.zoneName, anyValue)

        const result = await providerFuncs.getProvider({ provider })
        assertEqualForEachField(result, { ...params, err: '' })
      })
      it('versionを取得できること', async () => {
        assert.strictEqual(await providerFuncs.version({ provider }), BASE.APP.VERSION, 'version')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('Admin権限ではない署名の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProvider({ provider, accounts, options: { eoaKey: BASE.EOA.PROV1 } })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = providerFuncs.addProvider({
          provider,
          accounts,
          options: { deadline: exceededDeadline },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('空providerId指定の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProvider({
          provider,
          accounts,
          options: { providerId: BASE.PROV.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_INVALID_VAL)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProvider({ provider, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
      })

      it('既存のProvIDを指定した場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProvider({ provider, accounts })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_ID_EXIST)
      })
    })
  })
})
