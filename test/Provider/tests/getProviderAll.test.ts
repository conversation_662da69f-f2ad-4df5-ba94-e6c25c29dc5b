import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getProviderAll()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('取得したprovider情報が初期値であること', async () => {
        const result = await providerFuncs.getProviderAll({ provider, providerId: BASE.PROV.EMPTY.ID })
        await expect(result.providerId).to.be.equal(BASE.PROV.EMPTY.ID)
        await expect(result.providerData).to.be.deep.equal(
          Object.values({
            role: BASE.PROV.EMPTY.ID,
            name: BASE.PROV.EMPTY.NAME,
            zoneId: String(BASE.EMPTY.ZONE_ID),
            enabled: false,
          }),
        )
        await expect(result.providerEoa).to.be.equal(BASE.EMPTY.EOA)
        assert.strictEqual(result.zoneData.length, 0, 'zoneData should be an empty array')
      })
    })

    describe('provider情報が1件登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.modProvider({ provider, accounts, providerName: BASE.PROV.PROV0.NAME })
      })

      it('provider情報が1件取得できること', async () => {
        const result = await providerFuncs.getProviderAll({ provider, providerId: BASE.PROV.PROV0.ID })

        // resultが1件のみ返却されていることを確認
        assert.strictEqual(Object.values(result).length, 4, 'result should contain exactly one provider')

        await expect(result.providerId).to.be.equal(BASE.PROV.PROV0.ID)
        await expect(result.providerData).to.be.deep.equal(
          Object.values({
            role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
            name: BASE.PROV.PROV0.NAME,
            zoneId: String(BASE.ZONE_ID.ID0),
            enabled: true,
          }),
        )
        await expect(result.providerEoa).to.be.equal(BASE.PROV.EOA_ADDRESS)

        // zoneData配列内の要素を確認
        assertEqualForEachField(result.zoneData[0], {
          zoneId: String(BASE.ZONE_ID.ID0),
          zoneName: BASE.ZONE_NAME.NAME0,
        })
      })
    })

    describe('provider情報が1件zoneIdが複数登録されている状態', () => {
      before(async () => {
        await providerFuncs.addBizZone({ provider, accounts })
      })

      it('zoneData情報が2件取得できること', async () => {
        const result = await providerFuncs.getProviderAll({ provider, providerId: BASE.PROV.PROV0.ID })

        // resultが1件のみ返却されていることを確認
        assert.strictEqual(Object.values(result).length, 4, 'result should contain exactly 1 provider')
        // zoneDataが2件取得できることを確認
        assert.strictEqual(result.zoneData.length, 2, 'result should contain exactly 2 zoneData')
        // zoneData配列内の要素を確認
        assertEqualForEachField(result.zoneData[0], {
          zoneId: String(BASE.ZONE_ID.ID0),
          zoneName: BASE.ZONE_NAME.NAME0,
        })
        assertEqualForEachField(result.zoneData[1], {
          zoneId: String(BASE.ZONE_ID.ID1),
          zoneName: BASE.ZONE_NAME.NAME1,
        })
      })
    })
  })
})
