import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ModTokenOption, ProviderInstance, TokenInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline, toBytes32 } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modToken()', () => {
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  const PROV_ID = BASE.PROV.PROV0.ID

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
      })

      it('tokenの要素(name, symbol)が変更できること', async () => {
        const params: Pick<ModTokenOption, 'name' | 'symbol'> = {
          name: BASE.TOKEN.TOKEN2.NAME,
          symbol: BASE.TOKEN.TOKEN2.SYMBOL,
        }

        const tx = await providerFuncs.modToken({ provider, accounts, options: params })

        await expect(tx).to.emit(token, 'ModToken').withArgs(BASE.TOKEN.TOKEN1.ID, params.name, params.symbol, anyValue)
        const result = await providerFuncs.getToken({ provider, options: [PROV_ID] })
        assertEqualForEachField(result, { ...params, tokenId: BASE.TOKEN.TOKEN1.ID })
      })
    })

    describe('tokenの要素が更新されている状態(name: BASE.TOKEN.TOKEN2.NAME, symbol: BASE.TOKEN.TOKEN2.SYMBOl)', () => {
      it('tokenの要素(name)が変更できること', async () => {
        const params: Pick<ModTokenOption, 'name'> = { name: BASE.TOKEN.TOKEN1.NAME }

        const tx = await providerFuncs.modToken({ provider, accounts, options: params })

        await expect(tx)
          .to.emit(token, 'ModToken')
          .withArgs(BASE.TOKEN.TOKEN1.ID, params.name, BASE.TOKEN.TOKEN2.SYMBOL, anyValue)
        const result = await providerFuncs.getToken({ provider, options: [PROV_ID] })
        assertEqualForEachField(result, {
          ...params,
          tokenId: BASE.TOKEN.TOKEN1.ID,
          symbol: BASE.TOKEN.TOKEN2.SYMBOL,
        })
      })
    })

    describe('tokenの要素が更新されている状態(name: BASE.TOKEN.TOKEN1.NAME)', () => {
      it('tokenの要素(symbol)が変更できること', async () => {
        const params: Pick<ModTokenOption, 'symbol'> = { symbol: BASE.TOKEN.TOKEN1.SYMBOL }

        const tx = await providerFuncs.modToken({ provider, accounts, options: params })

        await expect(tx)
          .to.emit(token, 'ModToken')
          .withArgs(BASE.TOKEN.TOKEN1.ID, BASE.TOKEN.TOKEN1.NAME, params.symbol, anyValue)
        const result = await providerFuncs.getToken({ provider, options: [PROV_ID] })
        assertEqualForEachField(result, {
          ...params,
          tokenId: BASE.TOKEN.TOKEN1.ID,
          name: BASE.TOKEN.TOKEN1.NAME,
        })
      })
    })

    // describe('tokenの要素が更新されている状態(symbol: BASE.TOKEN.TOKEN1.SYMBOL)', () => {
    //   it('tokenの要素(pegKind)が変更できること', async () => {
    //     const params: Pick<ModTokenOption, 'pegKind'> = { pegKind: BASE.TOKEN.TOKEN1.PEGKIND };

    //     const tx = await providerFuncs.modToken(provider, accounts, params);

    //     const newTx = await truffleAssert.createTransactionResult(token, tx.tx);
    //     assertEventEmitted(newTx, 'ModToken', {
    //       ...params,
    //       tokenId: BASE.TOKEN.TOKEN1.ID,
    //       name: BASE.TOKEN.TOKEN1.NAME,
    //       symbol: BASE.TOKEN.TOKEN1.SYMBOL,
    //     });
    //     const result = await providerFuncs.getToken(provider, PROV_ID);
    //     assertEqualForEachField(result, {
    //       ...params,
    //       tokenId: BASE.TOKEN.TOKEN1.ID,
    //       name: BASE.TOKEN.TOKEN1.NAME,
    //       symbol: BASE.TOKEN.TOKEN1.SYMBOL,
    //     });
    //   });
    // });
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
      })

      it('未登録tokenIdで変更の場合、エラーがスローされること', async () => {
        const result = providerFuncs.modToken({
          provider,
          accounts,
          options: { tokenId: BASE.TOKEN.TOKEN2.ID },
        })
        await expect(result).to.be.revertedWith(ERR.TOKEN.TOKEN_ID_NOT_EXIST)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = providerFuncs.modToken({ provider, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('タイムアウトが発生の場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = providerFuncs.modToken({ provider, accounts, options: { deadline: exceededDeadline } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })
    })
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider not role', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { providerId: toBytes32('0x103') },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: { providerId: toBytes32('0x103') },
        })
        await providerFuncs.addToken({
          provider,
          accounts,
          options: { providerId: toBytes32('0x103') },
        })
      })

      it('should revert when provider not role', async () => {
        const result = providerFuncs.modToken({ provider, accounts, options: { eoaKey: BASE.EOA.ACCOUNT } })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_ROLE)
      })
    })
  })
})
