import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('checkTransfer()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    describe('準正常系', () => {
      before(async () => {
        ;({ accounts, provider, issuer, validator, financialZoneAccount, token } =
          await contractFixture<FinancialZoneAccountContractType>())
      })

      describe('account is not valid', () => {
        before(async () => {
          await providerFuncs.addProvider({ provider, accounts })
          await providerFuncs.addProviderRole({ provider, accounts })
          await issuerFuncs.addIssuer({ issuer, accounts })
          await issuerFuncs.addIssuerRole({ issuer, accounts })
          await validatorFuncs.addValidator({ validator, accounts })
          await providerFuncs.addToken({ provider, accounts })
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          })
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
          })
          await tokenFuncs.mint({
            token,
            accounts,
            amount: 500,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          })
        })
      })

      it('should return false and error message when account is invalid', async () => {
        const result = await financialZoneAccount.checkTransfer(BASE.ACCOUNT.EMPTY.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })

      it('should return false and error message when account is not registerd', async () => {
        const result = await financialZoneAccount.checkTransfer(BASE.ACCOUNT.ACCOUNT0.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })
  })
})

describe('checkTransfer()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    describe('準正常系', () => {
      before(async () => {
        ;({ provider, issuer, validator, financialZoneAccount, token } =
          await contractFixture<FinancialZoneAccountContractType>())
      })

      describe('account is not valid', () => {
        before(async () => {
          await providerFuncs.addProvider({ provider, accounts })
          await providerFuncs.addProviderRole({ provider, accounts })
          await issuerFuncs.addIssuer({ issuer, accounts })
          await issuerFuncs.addIssuerRole({ issuer, accounts })
          await validatorFuncs.addValidator({ validator, accounts })
          await providerFuncs.addToken({ provider, accounts })
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          })
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
          })
          await tokenFuncs.mint({
            token,
            accounts,
            amount: 500,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          })
        })
      })

      it('should return false and error message when account is invalid', async () => {
        const result = await financialZoneAccount.checkTransfer(BASE.ACCOUNT.EMPTY.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })

      it('should return false and error message when account is not registerd', async () => {
        const result = await financialZoneAccount.checkTransfer(BASE.ACCOUNT.ACCOUNT0.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })
  })
})
