import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, FinancialZoneAccountInstance } from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let account: AccountInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  describe('正常系', () => {
    before(async () => {
      ;({ account, financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    it('versionが取得する', async () => {
      assert.equal(await accountFuncs.version({ account }), BASE.APP.VERSION, 'version')
    })

    it('versionが取得する', async () => {
      assert.equal(await financialZoneAccount.version(), BASE.APP.VERSION, 'version')
    })
  })
})
