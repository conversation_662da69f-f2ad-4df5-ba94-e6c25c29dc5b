import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { getJSTDay } from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('syncCharge()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  // TODO: TokenコントラクトにaddCumlativeAmount()関数実装後修正する
  const cumulativeAmount = 200

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('cumulativeAmountが0の状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('日付が変わっていない場合、cumulativeAmountが加算されること', async () => {
        const jstDay = await getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeChargeAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeChargeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeAmount),
          Number(beforeResult.accountData.cumulativeAmount) + amount,
        )
      })

      it('日付が変わっていない場合、cumulativeChargeAmountが加算されること', async () => {
        const jstDay = await getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeChargeAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeChargeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount),
          Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount) + amount,
        )
      })

      it('日付が変った場合、cumulativeAmountがリセットされた上で加算されること', async () => {
        const amount = 100

        await time.increase(24 * 60 * 60)
        const jstDay = await getJSTDay()

        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount,
          cumulativeAmount: amount,
          cumulativeChargeAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeChargeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeAmount), amount)
      })

      it('日付が変った場合、cumulativeChargeAmountがリセットされた上で加算されること', async () => {
        const amount = 100

        await time.increase(24 * 60 * 60)
        const jstDay = await getJSTDay()

        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount,
          cumulativeAmount: amount,
          cumulativeChargeAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeChargeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount), amount)
      })

      it('日付が変った場合、Charge実行後にcumulativeChargeAmountが加算され、cumulativeMintAmountとcumulativeBurnAmountがリセットされること', async () => {
        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const mintAmount = 200
        const burnAmount = 50
        const chargeAmount = 75

        // First day: Perform mint, burn and charge operations
        await financialZoneAccountFuncs.syncMint({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, mintAmount, BASE.TRACE_ID],
        })

        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, burnAmount, BASE.TRACE_ID],
        })

        await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, chargeAmount, BASE.TRACE_ID],
        })

        // Verify all amounts are set correctly after first day operations
        const firstDayResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), mintAmount)
        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), burnAmount)
        assert.equal(
          Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount),
          chargeAmount,
        )

        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const jstDay = await getJSTDay()

        // Second day: Perform only charge operation
        const newChargeAmount = 100
        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, newChargeAmount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: newChargeAmount,
          cumulativeAmount: newChargeAmount,
          cumulativeChargeAmount: newChargeAmount,
          traceId: BASE.TRACE_ID,
        }

        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeChargeAmount,
            expectParams.traceId,
          )

        // Verify that after the day change and charge operation:
        // 1. cumulativeChargeAmount is reset and set to the new charge amount
        // 2. cumulativeMintAmount is reset to 0
        // 3. cumulativeBurnAmount is reset to 0
        const secondDayResult = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(secondDayResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(secondDayResult.accountData.cumulativeAmount), newChargeAmount)
        assert.equal(
          Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeChargeAmount),
          newChargeAmount,
        )
        assert.equal(Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), 0)
        assert.equal(Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), 0)
      })
    })
  })
})
