import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { getJSTDay } from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('syncCumulativeReset()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('cumulativeAmountが設定されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await validatorFuncs.addAccount({ validator, accounts }) // LIMIT_AMOUNTS: [100, 200, 300, 400, 500],
      })
      it('cumulativeAmountが0に更新されること', async () => {
        const tx = await financialZoneAccountFuncs.syncCumulativeReset({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID],
        })

        const jstDay = await getJSTDay()
        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCumulativeReset')
          .withArgs(...Object.values(expectParams))

        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(result.accountData.cumulativeAmount), 0)
        assert.equal(Number(result.accountData.cumulativeDate), jstDay)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('共通領域アカウントが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
      })
      it('未登録accountIdを指定した場合、エラーがスローされること', async () => {
        const result = financialZoneAccountFuncs.syncCumulativeReset({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID],
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
