import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    it('should revert when initialized', async () => {
      const result = financialZoneAccount.initialize(await financialZoneAccount.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
