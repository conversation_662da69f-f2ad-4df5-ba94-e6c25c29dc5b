import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('getAccountLimitData()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  // getAccountLimitData のテストは account.getAccountLimit から呼ばれるため Account のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount, accounts } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がAccountではない場合、エラーがスローされること', async () => {
        const result = financialZoneAccount.connect(accounts[1]).getAccountLimitData(BASE.ACCOUNT.ACCOUNT0.ID) // validatorコントラクト以外をfromに設定する
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.NOT_ACCOUNT_CONTRACT)
      })
    })
  })
})
