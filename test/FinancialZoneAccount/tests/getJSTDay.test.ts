import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import { getJSTDay } from '@test/common/utils'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('getJSTDay()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('本日の0:00に対応するJSTのUNIX秒が返却されること', async () => {
        const result = await financialZoneAccount.getJSTDay()
        const expected = await getJSTDay()
        await expect(result).to.be.equal(expected)
      })
    })
  })
})
