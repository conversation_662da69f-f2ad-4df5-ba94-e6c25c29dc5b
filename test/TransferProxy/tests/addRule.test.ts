import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addRule', () => {
  let accounts: SignerWithAddress[]
  let transferProxy: TransferProxyInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
    })

    describe('初期状態', () => {
      for (let i = 0; i < 3; i++) {
        it(`Ruleを${i + 1}番目に追加する`, async () => {
          const rule = await accounts[i].getAddress()
          const position = i
          const tx = await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
          await expect(tx).emit(transferProxy, 'AddRule').withArgs(rule, position)
          const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
          assert.equal(rules[position], rule)
        })
      }
      it('Ruleを0番目に再追加する', async function () {
        const rule = await accounts[3].getAddress()
        const position = 0
        const tx = await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(tx).emit(transferProxy, 'AddRule').withArgs(rule, position)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules[0], rule, 'Rule')
      })
      it('Ruleを2番目に再追加する', async function () {
        const rule = await accounts[4].getAddress()
        const position = 1
        const tx = await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(tx).emit(transferProxy, 'AddRule').withArgs(rule, position)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules[1], rule, 'Rule')
      })
    })
  })

  describe('異常系', () => {
    before(async () => {
      ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
    })

    describe('初期状態', () => {
      it('初回登録時にPosition指定が0でない場合はエラー', async function () {
        const rule = await accounts[0].getAddress()
        const position = 1
        const result = transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_NOT_EXIST)
      })
    })

    describe('Ruleが2件登録されている状態', () => {
      before(async () => {
        for (let i = 0; i < 2; i++) {
          const rule = await accounts[i].getAddress()
          const position = i
          await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        }
      })

      it('既に登録されているRule(_nextRule)の場合はエラー', async function () {
        const rule = await accounts[1].getAddress()
        const position = 1
        const result = transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_EXIST)
      })
      it('既に登録されているRule(_firstRule)の場合はエラー', async function () {
        const rule = await accounts[0].getAddress()
        const position = 0
        const result = transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_EXIST)
      })
      it('範囲外のPositionを指定して登録する場合はエラー', async function () {
        const rule = await accounts[5].getAddress()
        const position = 10
        const result = transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_NOT_EXIST)
      })
    })
  })
})
