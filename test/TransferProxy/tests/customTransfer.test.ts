import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('customTransfer', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let ibcToken: IBCTokenInstance
  let transferProxy: TransferProxyInstance
  let contractManager: ContractManagerInstance
  let customTransfer1: TransferableMock1Instance
  let customTransfer2: TransferableMock2Instance
  let customTransfer3: TransferableMock3Instance

  describe('正常系', () => {
    before(async () => {
      ;({
        accounts,
        provider,
        issuer,
        validator,
        account,
        transferProxy,
        ibcToken,
        contractManager,
        customTransfer1,
        customTransfer2,
        customTransfer3,
      } = await contractFixture<TransferProxyContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
      })

      it('カスタムコントラクトが登録されていない状態でCustomTransferを実行してもEventが発火されない', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('Discount')
        const miscValue2 = utils.toBytes32('0')
        const tx = await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        // Attach the contract instance with library ABI to get the event definition for the CustomTransfer event
        // Ref: https://ethereum.stackexchange.com/questions/128026/how-to-use-a-library-with-testcases-using-hardhat/147619#147619
        const transferProxyContractInstanceWithLibraryABI = await ethers.getContractAt(
          'ITransferable',
          await transferProxy.getAddress(),
          accounts[0],
        )
        await expect(tx).not.emit(transferProxyContractInstanceWithLibraryABI, 'CustomTransfer')
      })
    })

    describe('カスタムコントラクトが登録されている状態', () => {
      before(async () => {
        // 擬似的にIssueVoucherを行いAccountId1の付加領域残高を¥1000の状態
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 1000
        const deadline = await utils.getDeadline()
        const ibcAddess = await accounts[0].getAddress()
        const ibcAccount = accounts[0]
        const signerAdmin = privateKey.key[BASE.EOA.ADMIN]
        const sig = privateKey.sig(signerAdmin, ['address', 'uint256'], [ibcAddess, deadline])
        await contractManager
          .connect(accounts[0])
          .setIbcApp(ibcAddess, BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER, deadline, sig[0])
        await ibcTokenFuncs.issueVoucher({
          ibcToken,
          from: ibcAccount,
          amount: amount,
          options: {
            accountId,
          },
        })

        // カスタムコントラクト1,2,3を追加する
        for (const customTransfer of [customTransfer1, customTransfer2, customTransfer3]) {
          const idx = [customTransfer1, customTransfer2, customTransfer3].indexOf(customTransfer)
          const rule = await customTransfer.getAddress()
          const position = idx
          await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        }
      })

      it('カスタムコントラクト1に対してCustomTransferを実行する', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 100
        const miscValue1 = utils.toBytes32('DeCurret1')
        const miscValue2 = utils.toBytes32('0')
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        // TransferableMock1コントラクトが実行され、100送金しようとするが10しか送金されない事を確認する。
        // CustomTransferイベントがカスタムコントラクトで発火されるが、コントラクトを跨ぐ為、Assertionできない。
        assert.equal(afterAmount, 990, 'CustomTransfer')
      })

      it('カスタムコントラクト2に対してCustomTransferを実行する', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 500
        const miscValue1 = utils.toBytes32('DeCurret2')
        const miscValue2 = utils.toBytes32('0')
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        // TransferableMock1コントラクトが実行され、500送金しようとするが50しか送金されない事を確認する。
        // CustomTransferイベントがカスタムコントラクトで発火されるが、コントラクトを跨ぐ為に残高の確認のみとする。
        assert.equal(afterAmount, 940, 'CustomTransfer')
      })

      it('カスタムコントラクト3に対してCustomTransferを実行する', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 600
        const miscValue1 = utils.toBytes32('DeCurret3')
        const miscValue2 = utils.toBytes32('0')
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        // TransferableMock1コントラクトが実行され、600送金しようとするが90しか送金されない事を確認する。
        // CustomTransferイベントがカスタムコントラクトで発火されるが、コントラクトを跨ぐ為に残高の確認のみとする。。
        assert.equal(afterAmount, 850, 'CustomTransfer')
      })
      it('該当しないカスタムコントラクトを実行し、通常のTransferが実行される事を確認する。', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 600
        const miscValue1 = utils.toBytes32('test')
        const miscValue2 = utils.toBytes32('test')
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        // TransferのEventがEmitされるが、コントラクトを跨ぐ為に残高の確認のみとする。
        assert.equal(afterAmount, 250, 'CustomTransfer')
      })
    })
  })
})
