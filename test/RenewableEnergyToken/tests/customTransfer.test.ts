import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  StructType,
  TokenStatus,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('customTransfer', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let transferProxy: TransferProxyInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, renewableEnergyToken, transferProxy } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            accountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          },
        })
        await validatorFuncs.addAccount({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await renewableEnergyToken.getAddress(),
          position: 0,
        })
      })

      it('CustomTransferを実行した時に、Tokenが移転されること', async function () {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const result = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32],
        })
        const expected: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        }
        utils.assertEqualForEachField(result.renewableEnergyTokenData, expected)
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        assert.equal(afterAmount, 0, 'CustomTransfer')
      })

      it('miscValue1がrenewableではない場合、通常の移転が動作すること', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('dummy')
        const miscValue2 = BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        assert.equal(afterAmount, 0, 'CustomTransfer')
      })
      it('miscValue2に二つのtokenIdを含めると、二つ分移転が完了すること', async () => {
        // 事前にToken2とToken3をMint
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING},${BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_STRING}`
        await transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const result1 = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32],
        })
        const result2 = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_BYTES32],
        })
        const expected1: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        }
        const expected2: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        }
        utils.assertEqualForEachField(result1.renewableEnergyTokenData, expected1)
        utils.assertEqualForEachField(result2.renewableEnergyTokenData, expected2)
        const accountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        const afterAmount = accountData.accountData.balance
        assert.equal(afterAmount, String(0), 'CustomTransfer')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, renewableEnergyToken, transferProxy } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            accountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          },
        })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await renewableEnergyToken.getAddress(),
          position: 0,
        })
      })

      it('miscValue2が空の場合、エラーが出力されること', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = ``
        const result = transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_INVALID_VAL)
      })

      it('ownerAccountId != toAccountIdではない場合、エラーが出力されること', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING
        const result = transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_FROM_TO_ARE_SAME)
      })
      it('存在しないNFTを指定した場合は移転できないこと', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING
        const result = transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_NOT_EXIST)
      })
      it('miscValue2に複数のtokenIdが含まれている状態で、片方のtokenIdが存在しない場合に移転が失敗すること', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING},${BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_STRING}`
        const result = transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_NOT_EXIST)
      })
      it('miscValue2に複数のtokenIdが含まれている状態で、片方のtokenIdをfromAccountが所有していない場合に移転に失敗すること', async () => {
        // アカウント1以外のアカウントにtokenを追加でmint
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN2.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN2.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT2.ID,
          isLocked: false,
        })

        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 0
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING},${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING}`
        const result = transferProxyFuncs.customTransfer({
          transferProxy: transferProxy,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_NOT_OWNER)
      })
    })
  })
})
