import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { assertEqualBytes32 } from '@test/common/utils'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('registerEscrowAccount()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('初期状態', () => {
      it('EscrowAccountが登録できること', async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })

        const registeredEscrowAccount = await jpyTokenTransferBridge.escrowAccount(BASE.ZONE.FIN, BASE.ZONE.BIZ)
        assertEqualBytes32(registeredEscrowAccount, BASE.BRIDGE.ESCROW_ACCOUNT)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    // HACK: Admin権限でない署名の場合のケースは、TokenMock.checkAdminRoleがtrueしか返さないため実施不可

    describe('EscrowAccountが登録されている状態', () => {
      before(async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
      })
      it('重複してEscrowAccountを登録しようとした場合、エラーがスローされること', async () => {
        const result = jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_APP_JPYTT_ESCROW_ALWAYS_REG)
      })
    })
  })
})
