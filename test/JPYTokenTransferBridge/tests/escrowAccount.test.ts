import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { assertEqualBytes32 } from '@test/common/utils'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { before } from 'mocha'

describe('escrowAccount()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })
    describe('初期状態', () => {
      it('EscrowAccountが未登録の場合は0が返ること', async () => {
        assertEqualBytes32(
          await jpyTokenTransferBridge.escrowAccount(BASE.ZONE.FIN, BASE.ZONE.BIZ),
          BASE.BRIDGE._NO_ACCOUNT,
        )
      })
    })

    describe('EscrowAccountが登録されている状態', () => {
      before(async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
      })

      it('EscrowAccountが登録されている場合はIDが返ること', async () => {
        const registeredEscrowAccount = await jpyTokenTransferBridge.escrowAccount(BASE.ZONE.FIN, BASE.ZONE.BIZ)
        assertEqualBytes32(registeredEscrowAccount, BASE.BRIDGE.ESCROW_ACCOUNT)
      })
    })
  })
})
