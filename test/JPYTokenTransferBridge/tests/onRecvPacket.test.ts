import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IBCHandlerInstance, IBCTokenMockInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import Web3 from 'web3'

declare let web3: Web3

describe('onRecvPacket()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let ibcTokenMock: IBCTokenMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ib<PERSON><PERSON><PERSON><PERSON>, ibcTokenMock } =
        await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('EscrowAccountが登録されていて、残高を保持している状態', () => {
      let timeoutHeight
      before(async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
      })

      beforeEach(async () => {
        // IssueVoucherを実行し、アカウントの残高を補填する。
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)
        timeoutHeight = **********
        // TransferToEscrowを実行しEscrowAccountの残高を補填する。
        await jpyTokenTransferBridgeFuncs.transfer({ jpyTokenTransferBridge })
      })

      it('(TransferFromEscrow)FinZoneにおいてBizZoneからpacketを受信した場合に、EscrowAccountの残高が減算されていること', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        // FinZoneにおいてBizZoneからpacketを受信した場合、TransferFromEscrowが実行される。
        // JPYTokenTransferBridgeのonRecvPacket関数はIBCから呼び出すことを制限しているため、ibcHandler経由で呼び出すようにする。
        await jpyTokenTransferBridgeFuncs.recvPacket({
          ibcHandler,
          jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.BIZ,
            toZoneId: BASE.ZONE.FIN,
          },
        })

        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        await expect(accountBalance[0]).to.be.equal(
          BigInt(accountBalanceBefore[0]) + BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT),
        )
        await expect(escrowBalance[0]).to.be.equal(BigInt(escrowBalanceBefore[0]) - BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })

      it('(issueVoucher)BizZoneにおいてFinZoneからpacketを受信した場合に、BizZoneアカウントの残高が加算されていること', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)

        await jpyTokenTransferBridgeFuncs.recvPacket({
          ibcHandler,
          jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.FIN,
            toZoneId: BASE.ZONE.BIZ,
          },
        })

        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)

        await expect(accountBalance[0]).to.be.equal(
          BigInt(accountBalanceBefore[0]) + BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT),
        )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ibcHandler } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('初期状態', () => {
      it('packet空の場合、エラーがスローされること', async () => {
        const packetData = web3.eth.abi.encodeParameters([], [])
        const result = jpyTokenTransferBridgeFuncs.recvPacket({
          ibcHandler,
          jpyTokenTransferBridge,
          options: {
            packetData,
          },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })

      it('EscrowAccountが登録されていない場合、エラーがスローされること', async () => {
        const result = jpyTokenTransferBridgeFuncs.recvPacket({
          ibcHandler,
          jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.BIZ,
            toZoneId: BASE.ZONE.FIN,
          },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_APP_JPYTT_ESCROW_NOT_REG)
      })
    })
  })
})
