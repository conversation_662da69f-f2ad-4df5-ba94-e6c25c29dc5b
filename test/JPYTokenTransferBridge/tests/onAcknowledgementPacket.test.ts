import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IBCHandlerInstance, IBCTokenMockInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('onAcknowledgementPacket()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let ibcTokenMock: IBCTokenMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ibc<PERSON><PERSON><PERSON>, ibcTokenMock } =
        await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('EscrowAccountが登録されていて、EscrowAccountが残高を保持している状態', () => {
      before(async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
      })

      beforeEach(async () => {
        // IssueVoucherを実行し、アカウントの残高を補填する。
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)

        // TransferToEscrowを実行しEscrowAccountの残高を補填する。
        await jpyTokenTransferBridgeFuncs.transfer({ jpyTokenTransferBridge })
      })

      it('FinZoneからBizZoneへの送金が成功した場合、EscrowAccountからFinZoneアカウントに返金されないこと', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        await jpyTokenTransferBridgeFuncs.acknowledgementPacket({
          ibcHandler,
          jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.FIN,
            toZoneId: BASE.ZONE.BIZ,
          },
        })
        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        await expect(accountBalance[0]).to.be.equal(BigInt(accountBalanceBefore[0]))

        await expect(escrowBalance[0]).to.be.equal(BigInt(escrowBalanceBefore[0]))
      })

      // 返金のパターンは現在実装不可
      // it("FinZoneからBizZoneへの送金が失敗した場合、EscrowAccountからFinZoneアカウントに返金されること", async () => {
      //   const accountBalanceBefore = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ACCOUNT_A
      //   );
      //   const escrowBalanceBefore = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ESCROW_ACCOUNT
      //   );

      //   await jpyTokenTransferBridgeFuncs.acknowledgementPacket(
      //     ibcHandler,
      //     jpyTokenTransferBridge,
      //     {
      //       ack: BASE.IBC.INVALID_ACK,
      //     }
      //   );

      //   const accountBalance = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ACCOUNT_A
      //   );
      //   const escrowBalance = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ESCROW_ACCOUNT
      //   );

      //   assertEqualBn(
      //     accountBalance[0],
      //     accountBalanceBefore[0].add(
      //       web3.utils.toBN(BASE.BRIDGE.EXCHANGE_AMOUNT)
      //     )
      //   );
      //   assertEqualBn(
      //     escrowBalance[0],
      //     escrowBalanceBefore[0].sub(
      //       web3.utils.toBN(BASE.BRIDGE.EXCHANGE_AMOUNT)
      //     )
      //   );
      // });
    })

    describe('BizZoneアカウントが残高を保持している状態', () => {
      beforeEach(async () => {
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)
      })

      // 返金のパターンは現在実装不可
      // it("BizZoneからFinZoneへの送金が失敗した場合、BizZoneアカウントに返金されること", async () => {
      //   // BizZoneからFinZoneへのTransferを実行するので、RedeemVoucherが実行される。
      //   await jpyTokenTransferBridgeFuncs.transfer(jpyTokenTransferBridge, {
      //     fromZoneId: BASE.ZONE.BIZ,
      //     toZoneId: BASE.ZONE.FIN,
      //     amount: BASE.BRIDGE.EXCHANGE_AMOUNT,
      //   });

      //   const accountBalanceBefore = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ACCOUNT_A
      //   );

      //   await jpyTokenTransferBridgeFuncs.acknowledgementPacket(
      //     ibcHandler,
      //     jpyTokenTransferBridge,
      //     {
      //       amount: BASE.BRIDGE.EXCHANGE_AMOUNT,
      //       ack: BASE.IBC.INVALID_ACK,
      //     }
      //   );

      //   const accountBalance = await tokenMock.balanceOf(
      //     BASE.ZONE.FIN,
      //     BASE.BRIDGE.ACCOUNT_A
      //   );

      //   assertEqualBn(
      //     accountBalance[0],
      //     accountBalanceBefore[0].add(
      //       web3.utils.toBN(BASE.BRIDGE.EXCHANGE_AMOUNT)
      //     )
      //   );
      // });
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ibcHandler } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('初期状態', () => {
      // it("送金額に0設定された場合、エラーがスローされること", async () => {
      //   // TODO: Refactor. This function probably is useless. It falis because ack.
      //   // The recvPackage and transfer will revert it first.
      //   await truffleAssert.reverts(
      //     jpyTokenTransferBridgeFuncs.acknowledgementPacket(
      //       ibcHandler,
      //       jpyTokenTransferBridge,
      //       {
      //         amount: 0,
      //         ack: BASE.IBC.INVALID_ACK,
      //       }
      //     ),
      //     ERR.IBC.IBC_INVALID_VAL
      //   );
      // });
      // it("送金先と送金元に同一の領域が設定された場合、エラーがスローされること", async () => {
      //   // TODO: Refactor. This function probably is useless. It falis because ack.
      //   // The recvPackage and transfer will revert it first.
      //   await truffleAssert.reverts(
      //     jpyTokenTransferBridgeFuncs.acknowledgementPacket(
      //       ibcHandler,
      //       jpyTokenTransferBridge,
      //       {
      //         fromZoneId: BASE.ZONE.FIN,
      //         toZoneId: BASE.ZONE.FIN,
      //         ack: BASE.IBC.INVALID_ACK,
      //       }
      //     ),
      //     ERR.IBC.IBC_INVALID_VAL
      //   );
      // });
      // DELETE: Transfer will fail when EscrowAccount is not registed.
      // no need to test it here.
    })
  })
})
