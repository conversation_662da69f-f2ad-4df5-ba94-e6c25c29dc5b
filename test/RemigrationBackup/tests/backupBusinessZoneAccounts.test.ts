import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  RemigrationBackupInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('backupBusinessZoneAccounts()', () => {
  let accounts: SignerWithAddress[]
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let contractManager: ContractManagerInstance
  let remigrationBackup: RemigrationBackupInstance
  describe('正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      ;({
        accounts,
        provider,
        issuer,
        validator,
        token,
        ibcToken,
        businessZoneAccount,
        remigrationBackup,
        contractManager,
      } = await contractFixture<RemigrationBackupContractType>())

      ibcAddress = await accounts[0]
      ibcAddressString = await ibcAddress.getAddress()
    })

    describe('businessZoneAccountsが登録されていない状態', () => {
      it('空リストが取得できること', async () => {
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: 0,
          limit: 10,
        })
        utils.assertEqualForEachField(result, {
          bizAccountsAll: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('businessZoneAccountsが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 600,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('offset0, limit10を指定した場合、1ページ目1項目目から3件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 3, err: '' })
        utils.assertEqualForEachField(result.bizAccountsAll[0], { accountId: toBytes32('x400') })
        utils.assertEqualForEachField(result.bizAccountsAll[0].bizAccountsByZoneId[0], {
          zoneId: '3001',
          accountIdExistenceByZoneId: true,
        })
        utils.assertEqualForEachField(result.bizAccountsAll[0].bizAccountsByZoneId[0].businessZoneAccountData, {
          accountName: 'ACCOUNT1',
          balance: '0',
          accountStatus: toBytes32('active'),
        })
        utils.assertEqualForEachField(result.bizAccountsAll[1], { accountId: toBytes32('x401') })
        utils.assertEqualForEachField(result.bizAccountsAll[1].bizAccountsByZoneId[0], {
          zoneId: '3001',
          accountIdExistenceByZoneId: true,
        })
        utils.assertEqualForEachField(result.bizAccountsAll[1].bizAccountsByZoneId[0].businessZoneAccountData, {
          accountName: 'ACCOUNT1',
          balance: '300',
          accountStatus: toBytes32('active'),
        })
        utils.assertEqualForEachField(result.bizAccountsAll[2], {
          accountId: toBytes32('x402'),
          bizAccountsByZoneId: [],
        })
        utils.assertEqualForEachField(result.bizAccountsAll[2].bizAccountsByZoneId, []) // bizAccountsByZoneId is already an empty array, don't need to check with index 0
      })

      it('最初の1件が取得できること', async () => {
        const offset = 0
        const limit = 1
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 3, err: '' })
        utils.assertEqualForEachField(result.bizAccountsAll[0], { accountId: toBytes32('x400') })
      })

      it('最後の1件が取得できること', async () => {
        const offset = 1
        const limit = 1
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 3, err: '' })
        utils.assertEqualForEachField(result.bizAccountsAll[0], { accountId: toBytes32('x401') })
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        utils.assertEqualForEachField(result.bizAccountsAll, [])
      })

      it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = 3
        const limit = 20
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX,
        })
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            eoaKey: 9,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            deadline: now,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
