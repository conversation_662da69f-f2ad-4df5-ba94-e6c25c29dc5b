import { accountFuncs } from '@test/Account/helpers/function'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = toBytes32('x490')

describe('transferToEscrow()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]
  let ibcAddress
  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager, businessZoneAccount } =
        await contractFixture<IBCTokenContractType>())
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('miscValueが空, sendAccountIdがfromAccountIdで指定した値でTransferが実行されること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 100

        const tx = await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount,
          options: {
            fromAccountId,
            toAccountId,
          },
        })

        const expectParams = {
          transferData: {
            transferType: BASE.TOKEN.TRANSFER_TYPE.CHARGE,
            zoneId: BASE.ZONE_ID.ID0,
            fromValidatorId: BASE.VALID.VALID0.ID,
            toValidatorId: BASE.VALID.VALID0.ID,
            fromAccountBalance: BigInt(300 - 100),
            toAccountBalance: BigInt(0 + 100),
            businessZoneBalance: BigInt(0 + 100),
            bizZoneId: BASE.ZONE_ID.ID1,
            sendAccountId: fromAccountId,
            fromAccountId: fromAccountId,
            fromAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            toAccountId: toAccountId,
            toAccountName: BASE.ACCOUNT.EMPTY.NAME, // toAccountはEscrowアカウントなのでnameが空
            amount: BigInt(amount),
            miscValue1: toBytes32(''),
            miscValue2: '',
            memo: '',
          },
          traceId: BASE.TRACE_ID,
        }
        await expect(tx).to.emit(ibcToken, 'Transfer').withArgs(Object.values(expectParams.transferData), BASE.TRACE_ID)
        const fromAccountData = await accountFuncs.getAccount({ account, params: [fromAccountId] })
        assertEqualForEachField(fromAccountData.accountData, { balance: 300 - 100 })
        const toAccountData = await accountFuncs.getAccount({ account, params: [toAccountId] })
        assertEqualForEachField(toAccountData.accountData, { balance: 0 + 100 })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager, businessZoneAccount } =
        await contractFixture<IBCTokenContractType>())
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, TERMINATED_ACCOUNT1]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: TERMINATED_ACCOUNT1,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: TERMINATED_ACCOUNT1,
          },
        })
      })

      it('呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 100

        const result = ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: accounts[1],
          amount,
          options: { fromAccountId, toAccountId },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.NOT_IBC_CONTRACT)
      })

      it('balanceが不足している場合、エラーがスローされること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 2000

        const result = ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount,
          options: { fromAccountId, toAccountId },
        })
        await expect(result).to.be.revertedWith(ERR.TOKEN.TOKEN_BALANCE_NOT_ENOUGH)
      })
    })
  })
})
