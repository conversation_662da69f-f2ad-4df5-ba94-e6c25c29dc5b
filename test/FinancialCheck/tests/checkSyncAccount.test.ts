import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline, siginfoGenerator, toBytes } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { ethers } from 'hardhat'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'

describe('checkSyncAccount()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let financialCheck: FinancialCheckInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]
  let ibcToken: IBCTokenInstance

  describe('正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      ;({ accounts, provider, issuer, validator, token, financialCheck, contractManager, businessZoneAccount } =
        await contractFixture<FinancialCheckContractType>())
      ibcAddress = await accounts[0]
      ibcAddressString = await ibcAddress.getAddress()
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 600,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          },
        })
      })

      it('アカウント申込可能かどうか確認できること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('強制償却済みのアカウント申込可能かどうか確認できること', async () => {
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
        await issuerFuncs.forceBurn({
          issuer,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        const status = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT2.ID],
        })
        assertEqualForEachField(status.accountDataAll, { accountStatus: BASE.STATUS.FORCE_BURNED })

        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })

        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          zoneId: BASE.ZONE_ID.ID1,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      ibcAddress = await accounts[0]
      ibcAddressString = await ibcAddress.getAddress()
      ;({
        accounts,
        provider,
        issuer,
        validator,
        token,
        financialCheck,
        contractManager,
        businessZoneAccount,
        ibcToken,
      } = await contractFixture<FinancialCheckContractType>())
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)

      before(async () => {
        const deadline = await getDeadline()
        const params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i],
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: { issuerId: BASE.ISSUER.ISSUER1.ID, bankCode: BASE.ISSUER.ISSUER1.BANK_CODE },
        })
        await issuerFuncs.addIssuerRole({
          issuer,
          accounts,
          options: { issuerId: BASE.ISSUER.ISSUER1.ID },
        })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await validatorFuncs.addValidator({
          validator,
          accounts,
          options: { issuerId: BASE.ISSUER.ISSUER1.ID, validatorId: BASE.VALID.VALID1.ID },
        })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            accountId: BASE.ACCOUNT.ACCOUNT11.ID,
            accountName: BASE.ACCOUNT.ACCOUNT11.NAME,
          },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          },
        })
      })

      it('認可しているイシュアに紐づかないアカウントの場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID1.ID,
          accountId: BASE.ACCOUNT.ACCOUNT11.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('バリデータが未入力の場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.EMPTY.ID,
          accountId: BASE.ACCOUNT.ACCOUNT20.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.VALID.VALIDATOR_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })

      it('アカウントが未入力の場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.EMPTY.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.COMMON.INVALID_ACCOUNT_ID,
        }
        assertEqualForEachField(result, expected)
      })

      it('存在しないアカウントの場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT20.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('開設申込時、アカウントが存在する場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          zoneId: BASE.ZONE_ID.ID1,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_NOT_FORCE_BURNED,
        }
        assertEqualForEachField(result, expected)
      })

      // it('バリデータに紐づいていないアカウントの場合、エラーが返却されること', async () => {
      //   await issuerFuncs.addIssuer(issuer, accounts, {
      //     issuerId: BASE.ISSUER.ISSUER10.ID,
      //   });
      //   await issuerFuncs.addIssuerRole(issuer, accounts, {
      //     issuerId: BASE.ISSUER.ISSUER10.ID,
      //   });
      //   await validatorFuncs.addValidator(validator, accounts, {
      //     validatorId: BASE.VALID.VALID20.ID,
      //     issuerId: BASE.ISSUER.ISSUER10.ID,
      //   });
      //   await validatorFuncs.addAccount(validator, accounts, {
      //     validatorId: BASE.VALID.VALID20.ID,
      //     accountId: BASE.ACCOUNT.ACCOUNT10.ID,
      //     accountName: BASE.ACCOUNT.ACCOUNT10.NAME,
      //   });

      //   const pt = await getDeadline();
      //   const siginfo = await getDeadline();

      //   const result = await financialCheckFuncs.checkSyncAccount(
      //     financialCheck,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ACCOUNT.ACCOUNT10.ID,
      //     BASE.ZONE_ID.ID0,
      //     BASE.STATUS.APPLYING,
      //     siginfo,
      //     {
      //       privateKeyForSig: privateKey.key[valid0.eoaKey],
      //       eoaKey: valid0.eoaKey,
      //     },
      //   );
      //   const expected = {
      //     success: false,
      //     err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
      //   };
      //   assertEqualForEachField(result, expected);
      // });

      it('解約申込時、存在しないアカウントを指定した場合にエラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.TERMINATING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      // it('バリデータ署名が不正である場合、エラーが返却されること', async () => {
      //   const pt = await getDeadline();
      //   const siginfo = await getDeadline();

      //   const result = await financialCheckFuncs.checkSyncAccount(
      //     financialCheck,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ZONE_ID.ID0,
      //     BASE.STATUS.APPLYING,
      //     siginfo,
      //     {
      //       privateKeyForSig: privateKey.key[valid0.eoaKey],
      //       eoaKey: valid1.eoaKey,
      //     },
      //   );
      //   const expected = {
      //     success: false,
      //     err: ERR.VALID.VALIDATOR_NOT_ROLE,
      //   };
      //   assertEqualForEachField(result, expected);
      // });

      // it('バリデータ署名が有効期限切れである場合、エラーが返却されること', async () => {
      //   const exceededDeadline = await getExceededDeadline();
      //   const pt = await getDeadline();
      //   const siginfo = await getDeadline();

      //   const result = await financialCheckFuncs.checkTransaction(
      //     financialCheck,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ZONE_ID.ID0,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     BASE.ACCOUNT.ACCOUNT2.ID,
      //     100,
      //     siginfo,
      //     {
      //       privateKeyForSig: privateKey.key[valid0.eoaKey],
      //       eoaKey: valid0.eoaKey,
      //       deadline: exceededDeadline,
      //     },
      //   );
      //   const expected = {
      //     success: false,
      //     err: ERR.ACTRL.ACTRL_SIG_TIMEOUT,
      //   };
      //   assertEqualForEachField(result, expected);
      // });

      it('アカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const invalidAccountSig = privateKey.sig(
          siginfo.signer,
          ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
          [
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
            10,
            BASE.ACCOUNT_SIG_MSG.TRANSFER,
          ],
        )[0]

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {
            accountSignature: invalidAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        assertEqualForEachField(result, expected)
      })

      it('空のアカウント署名が無効である場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)
        const emptyAccountSig = toBytes('')

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {
            accountSignature: emptyAccountSig,
          },
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
        }
        assertEqualForEachField(result, expected)
      })

      it('パラメータのaccountStatus が TERMINATING、かつ　Biz ゾーンのアカウント残高がゼロでない場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const sigInfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const ibcTokenAddress = await ibcToken.getAddress()
        await helpers.setBalance(ibcTokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(ibcTokenAddress)
        const fakeIbcToken = await ethers.getSigner(ibcTokenAddress)

        await businessZoneAccount
          .connect(fakeIbcToken)
          .addBusinessZoneBalance(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT2.ID, 100)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck: financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          zoneId: BASE.ZONE_ID.ID1,
          accountStatus: BASE.STATUS.TERMINATING,
          sigInfo: sigInfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_BALANCE_NOT_ZERO,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('アカウント開設申込されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID0,
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          },
        })
      })

      it('開設申込済の場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_NOT_FORCE_BURNED,
        }
        assertEqualForEachField(result, expected)
      })

      it('空validatorIdを指定した場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.EMPTY.ID,
          accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.APPLYING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.VALID.VALIDATOR_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('アカウント解約申込されている状態', () => {
      before(async () => {
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID0,
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })
      })

      it('解約申込済の場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.TERMINATING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_TERMINATING_OR_TERMINATED,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('アカウント解約されている状態', () => {
      before(async () => {
        await validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })
      })

      it('解約済の場合、エラーが返却されること', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.TERMINATING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_TERMINATING_OR_TERMINATED,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('account status is not valid to requirement', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(4, 10)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await Promise.all(
          params.map(async (v, i) => {
            await validatorFuncs.addAccount({
              validator,
              accounts,
              options: {
                validatorId: BASE.VALID.VALID0.ID,
                accountId: v.accountId,
                accountName: v.accountName,
              },
            })
            await issuerFuncs.addAccountRole({
              issuer,
              accounts,
              options: {
                issuerId: BASE.ISSUER.ISSUER0.ID,
                accountId: v.accountId,
                deadline: deadline + 60,
              },
            })
          }),
        )
      })

      it('should run normaly when status from business zone is not terminate', async () => {
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        // Set to frozen so line 274 will jump to else case
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.TERMINATING,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('should run normaly when input status is not terminate or applying', async () => {
        // #TODO: This test should be update when the function catch more status
        const pt = await getDeadline()
        const siginfo = await siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt)

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT4.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })

        const result = await financialCheckFuncs.checkSyncAccount({
          financialCheck,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT4.ID,
          zoneId: BASE.ZONE_ID.ID0,
          accountStatus: BASE.STATUS.FROZEN,
          sigInfo: siginfo,
          options: {},
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
