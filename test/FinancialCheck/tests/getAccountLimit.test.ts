import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('getAccountLimit()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialCheck: FinancialCheckInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, financialCheck, financialZoneAccount, token, accounts } =
        await contractFixture<FinancialCheckContractType>())
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
      })

      it('アカウントの限度額情報を取得できること', async () => {
        const result = await financialCheckFuncs.getAccountLimit({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expected = {
          accountLimitData: {
            mintLimit: BASE.LIMIT_VALUES.mint,
            burnLimit: BASE.LIMIT_VALUES.burn,
            chargeLimit: BASE.LIMIT_VALUES.charge,
            dischargeLimit: BASE.LIMIT_VALUES.discharge,
            transferLimit: BASE.LIMIT_VALUES.transfer,
            cumulativeLimit: BASE.LIMIT_VALUES.cumulative.total,
            cumulativeAmount: 0,
            cumulativeDate: 0,
            cumulativeTransactionLimits: {
              cumulativeMintLimit: BASE.LIMIT_VALUES.cumulative.mint,
              cumulativeMintAmount: 0,
              cumulativeBurnLimit: BASE.LIMIT_VALUES.cumulative.burn,
              cumulativeBurnAmount: 0,
              cumulativeChargeLimit: BASE.LIMIT_VALUES.cumulative.charge,
              cumulativeChargeAmount: 0,
              cumulativeDischargeLimit: BASE.LIMIT_VALUES.cumulative.discharge,
              cumulativeDischargeAmount: 0,
              cumulativeTransferLimit: BASE.LIMIT_VALUES.cumulative.transfer,
              cumulativeTransferAmount: 0,
            },
          },
          err: '',
        }
        await expect(result.err).to.deep.equal(expected.err)
        await expect(result.accountLimitData).to.deep.equal([
          expected.accountLimitData.mintLimit,
          expected.accountLimitData.burnLimit,
          expected.accountLimitData.chargeLimit,
          expected.accountLimitData.dischargeLimit,
          expected.accountLimitData.transferLimit,
          expected.accountLimitData.cumulativeLimit,
          expected.accountLimitData.cumulativeAmount,
          expected.accountLimitData.cumulativeDate,
          Object.values(expected.accountLimitData.cumulativeTransactionLimits),
        ])
      })

      it('cumulativeDateが昨日以前の場合、累積額が0でデータが返されること', async () => {
        // First, we need to add some cumulative amounts to the account
        // We'll use the financialZoneAccount contract to do this

        // Impersonate the token contract to call addCumlativeAmount
        await ethers.provider.send('hardhat_impersonateAccount', [await token.getAddress()])
        await ethers.provider.send('hardhat_setBalance', [await token.getAddress(), '0x1000000000000000000'])
        const fakeToken = await ethers.getSigner(await token.getAddress())

        // Add some cumulative amount
        await financialZoneAccount.connect(fakeToken).addCumlativeAmount(BASE.ACCOUNT.ACCOUNT1.ID, 500, BASE.TRACE_ID)

        // Add some transaction-specific cumulative amounts
        await financialZoneAccount.syncTransfer(BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID)

        await financialZoneAccount.syncMint(BASE.ACCOUNT.ACCOUNT1.ID, 300, BASE.TRACE_ID)

        // Advance time by 24 hours + 1 second to make cumulativeDate yesterday
        await time.increase(24 * 60 * 60 + 1)

        // Now call getAccountLimit and verify the cumulative amounts are reset
        const result = await financialCheckFuncs.getAccountLimit({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // Verify that all cumulative amounts are 0
        expect(Number(result.accountLimitData[6])).to.equal(0) // cumulativeAmount

        // Check that cumulativeTransactionLimits amounts are all 0
        const cumulativeTransactionLimits = result.accountLimitData[8]
        expect(Number(cumulativeTransactionLimits[1])).to.equal(0) // cumulativeMintAmount
        expect(Number(cumulativeTransactionLimits[3])).to.equal(0) // cumulativeBurnAmount
        expect(Number(cumulativeTransactionLimits[5])).to.equal(0) // cumulativeChargeAmount
        expect(Number(cumulativeTransactionLimits[7])).to.equal(0) // cumulativeDischargeAmount
        expect(Number(cumulativeTransactionLimits[9])).to.equal(0) // cumulativeTransferAmount
      })

      it('アカウントが存在しない場合、取得に失敗すること', async () => {
        const result = await financialCheckFuncs.getAccountLimit({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT20.ID],
        })
        const expected = {
          accountLimitData: {},
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })
      it('無効なアカウントIDを選択した場合、取得に失敗すること', async () => {
        const result = await financialCheckFuncs.getAccountLimit({
          financialCheck,
          params: [BASE.ACCOUNT.EMPTY.ID],
        })
        const expected = {
          accountLimitData: {},
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
