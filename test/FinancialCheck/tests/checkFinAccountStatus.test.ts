import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialCheckInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline, toBytes32 } from '@test/common/utils'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('checkFinAccountStatus()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let financialCheck: FinancialCheckInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, token, financialCheck, accounts } =
        await contractFixture<FinancialCheckContractType>())
    })

    describe('アカウントが登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await getDeadline()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({
          validator,
          accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const v of params) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: v.accountId,
              accountName: v.accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: v.accountId,
              deadline: deadline + 60,
            },
          })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('アカウントのステータスを取得できること', async () => {
        const result = await financialCheckFuncs.checkFinAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expected = {
          accountStatus: BASE.STATUS.ACTIVE,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('アカウントが存在しない場合、取得に失敗すること', async () => {
        const result = await financialCheckFuncs.checkFinAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT20.ID],
        })
        const expected = {
          accountStatus: toBytes32(''),
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })
      it('無効なアカウントIDを選択した場合、取得に失敗すること', async () => {
        const result = await financialCheckFuncs.checkFinAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.EMPTY.ID],
        })
        const expected = {
          accountStatus: toBytes32(''),
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
