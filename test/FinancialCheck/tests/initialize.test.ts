import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, FinancialCheckInstance } from '@test/common/types'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let financialCheck: FinancialCheckInstance
  let contractManager: ContractManagerInstance
  describe('正常系', () => {
    before(async () => {
      ;({ financialCheck, contractManager } = await contractFixture<FinancialCheckContractType>())
    })

    it('should revert when initialized', async () => {
      const result = financialCheck.initialize(await contractManager.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
