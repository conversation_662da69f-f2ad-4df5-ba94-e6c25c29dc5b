import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, SyncAccountOption, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('syncAccount()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addValidatorRole({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
        await validatorFuncs.addValidatorAccountId({ validator, accounts })
      })

      it('syncAccountでaccountが追加できること(approvalAmountが0)', async () => {
        const params: SyncAccountOption = {
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          accountName: BASE.ACCOUNT.ACCOUNT5.NAME,
          validatorId: BASE.VALID.VALID0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          accountStatus: BASE.STATUS.APPLYING,
          reasonCode: BASE.REASON_CODE1,
          approvalAmount: BASE.ZERO_APPROVAL_VALUE,
        }

        const tx = await validatorFuncs.syncAccount({ validator, accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'SyncAccount')
          .withArgs(
            params.validatorId,
            params.accountId,
            params.accountName,
            params.zoneId,
            params.zoneName,
            params.accountStatus,
            params.approvalAmount,
            BASE.TRACE_ID,
          )

        const accountData = await validatorFuncs.getAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT5.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
        }
        utils.assertEqualForEachField(accountData.accountData, expectedObj)

        //Validatorのデータを確認する
        const validatorHasAccountResult = await validatorFuncs.hasAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        utils.assertEqualForEachField(validatorHasAccountResult, { success: true, err: '' })
      })

      it('syncAccountでaccountが追加できること(approvalAmountが許可額上限値)', async () => {
        const params: SyncAccountOption = {
          accountId: BASE.ACCOUNT.ACCOUNT6.ID,
          accountName: BASE.ACCOUNT.ACCOUNT6.NAME,
          validatorId: BASE.VALID.VALID0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          accountStatus: BASE.STATUS.APPLYING,
          reasonCode: BASE.REASON_CODE1,
          approvalAmount: BASE.MAX_APPROVAL_VALUE,
        }

        const tx = await validatorFuncs.syncAccount({ validator, accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'SyncAccount')
          .withArgs(
            params.validatorId,
            params.accountId,
            params.accountName,
            params.zoneId,
            params.zoneName,
            params.accountStatus,
            params.approvalAmount,
            BASE.TRACE_ID,
          )

        const accountData = await validatorFuncs.getAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT6.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
        }
        utils.assertEqualForEachField(accountData.accountData, expectedObj)

        //Validatorのデータを確認する
        const validatorHasAccountResult = await validatorFuncs.hasAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        utils.assertEqualForEachField(validatorHasAccountResult, { success: true, err: '' })
      })

      it('syncAccountでaccountが解約できること', async () => {
        const params: SyncAccountOption = {
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          accountName: BASE.ACCOUNT.ACCOUNT5.NAME,
          validatorId: BASE.VALID.VALID0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          accountStatus: BASE.STATUS.TERMINATING,
          reasonCode: BASE.REASON_CODE1,
        }

        const tx = await validatorFuncs.syncAccount({ validator, accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'SyncAccount')
          .withArgs(
            params.validatorId,
            params.accountId,
            params.accountName,
            params.zoneId,
            params.zoneName,
            params.accountStatus,
            BASE.ZERO_APPROVAL_VALUE,
            BASE.TRACE_ID,
          )

        const accountData = await validatorFuncs.getAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT5.NAME,
          accountStatus: BASE.STATUS.TERMINATED,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: await utils.getLatestBlockTimestamp(),
        }
        utils.assertEqualForEachField(accountData.accountData, expectedObj)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('アカウント申し込み時、accountが既に紐付けられている場合、エラーがスローされること', async () => {
        const result = validatorFuncs.syncAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_EXIST)
      })

      it('アカウント解約申し込み時、accountが存在しない場合、エラーがスローされること', async () => {
        const result = validatorFuncs.syncAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })

    describe('accountStatus input is terminated', () => {
      it('should emit event SyncAccount but no data change when accountStatus input is terminated', async () => {
        // await validatorFuncs.addAccount(validator, accounts, { accountId: BASE.ACCOUNT.ACCOUNT12.ID });
        const tx = await validatorFuncs.syncAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            accountStatus: BASE.STATUS.TERMINATED,
          },
        })

        await expect(tx)
          .to.emit(validator, 'SyncAccount')
          .withArgs(
            BASE.VALID.VALID0.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.NAME,
            BASE.ZONE_ID.ID0,
            BASE.ZONE_NAME.NAME0,
            BASE.STATUS.TERMINATED,
            BASE.ZERO_APPROVAL_VALUE,
            BASE.TRACE_ID,
          )

        const accountData = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
        }
        utils.assertEqualForEachField(accountData.accountData, expectedObj)
      })
    })
  })
})
