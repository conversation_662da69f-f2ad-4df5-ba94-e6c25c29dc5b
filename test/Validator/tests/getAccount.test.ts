import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAccount()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('validatorに紐づくaccount情報が取得できること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
          mintLimit: 3000,
          burnLimit: 4000,
          chargeLimit: 2000,
          dischargeLimit: 4000,
          transferLimit: 1000,
          cumulativeLimit: 5000,
          cumulativeAmount: 0,
          cumulativeDate: 0,
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('')],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })

      it('未登録accountIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('x900')],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })

    describe('accountが解約状態', () => {
      before(async () => {
        await validatorFuncs.setTerminated({ validator, accounts })
      })

      it('アカウントの解約フラグが取得できること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.TERMINATED,
          balance: 0,
          reasonCode: BASE.REASON_CODE2,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: await utils.getLatestBlockTimestamp(),
          mintLimit: 3000,
          burnLimit: 4000,
          chargeLimit: 2000,
          dischargeLimit: 4000,
          transferLimit: 1000,
          cumulativeLimit: 5000,
          cumulativeAmount: 0,
          cumulativeDate: 0,
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })
    })
  })

  describe('正常系_BizZoneの場合', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態(BizZoneとして登録)', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('validatorに紐づくaccount情報が取得できること(Limit情報なし)', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
          mintLimit: 0,
          burnLimit: 0,
          chargeLimit: 0,
          transferLimit: 0,
          cumulativeLimit: 0,
          cumulativeAmount: 0,
          cumulativeDate: 0,
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('')],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })

      it('未登録accountIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('x900')],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })

    describe('accountが解約状態', () => {
      before(async () => {
        await validatorFuncs.setTerminated({ validator, accounts })
      })

      it('アカウントの解約フラグが取得できること', async () => {
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.TERMINATED,
          balance: 0,
          reasonCode: BASE.REASON_CODE2,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: await utils.getLatestBlockTimestamp(),
          mintLimit: 0,
          burnLimit: 0,
          chargeLimit: 0,
          dischargeLimit: 0,
          transferLimit: 0,
          cumulativeLimit: 0,
          cumulativeAmount: 0,
          cumulativeDate: 0,
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('validator.hasAccount return error', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
      })

      it('should return empty data and error message when validatorId is not validator', async () => {
        const tx = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.EMPTY.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(tx.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })

      it('should return empty data and error message when validatorId is not exist', async () => {
        const tx = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID2.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(tx.err, ERR.VALID.VALIDATOR_ID_NOT_EXIST, 'err')
      })

      it('should return empty data and error message when accountId is not validator', async () => {
        const tx = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.EMPTY.ID],
        })
        assert.equal(tx.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })

      it('should return empty data and error message when accountId is not exist', async () => {
        const tx = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT10.ID],
        })
        assert.equal(tx.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })
  })
})
