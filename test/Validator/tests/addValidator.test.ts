import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddValidatorOption, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import _ from 'lodash'
import { before } from 'mocha'

describe('addValidator()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          },
        })
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
      })

      it('validatorが登録できること', async () => {
        const params: AddValidatorOption = {
          validatorId: BASE.VALID.VALID0.ID,
          name: BASE.VALID.VALID0.NAME,
          issuerId: BASE.ISSUER.ISSUER0.ID,
        }

        const tx = await validatorFuncs.addValidator({ validator, accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'AddValidator')
          .withArgs(params.validatorId, params.issuerId, params.name, BASE.TRACE_ID)

        const result = await validatorFuncs.getValidator({ validator, prams: [params.validatorId] })
        utils.assertEqualForEachField(result, {
          ..._.omit(params, ['validatorId']),
          err: '',
        })
      })

      it('validatorが登録できること(名前が空文字指定)', async () => {
        const params: AddValidatorOption = {
          validatorId: BASE.VALID.VALID1.ID,
          name: BASE.VALID.EMPTY.NAME,
          issuerId: BASE.ISSUER.ISSUER1.ID,
        }

        const tx = await validatorFuncs.addValidator({ validator, accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'AddValidator')
          .withArgs(params.validatorId, params.issuerId, params.name, BASE.TRACE_ID)

        const result = await validatorFuncs.getValidator({ validator, prams: [params.validatorId] })
        utils.assertEqualForEachField(result, {
          ..._.omit(params, ['validatorId']),
          err: '',
        })
      })
    })

    describe('付加領域', () => {
      before(async () => {
        ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
      })

      describe('provider, providerRoleが登録されている状態', () => {
        before(async () => {
          await providerFuncs.addProvider({
            provider,
            accounts,
            options: {
              zoneId: BASE.ZONE_ID.ID1,
            },
          })
          await providerFuncs.addProviderRole({
            provider,
            accounts,
            options: {
              providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
            },
          })
        })

        it('登録されていないissuerを指定しても、validatorが登録できること', async () => {
          const params: AddValidatorOption = {
            validatorId: BASE.VALID.VALID0.ID,
            name: BASE.VALID.VALID0.NAME,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          }

          const tx = await validatorFuncs.addValidator({ validator, accounts, options: params })

          await expect(tx)
            .to.emit(validator, 'AddValidator')
            .withArgs(params.validatorId, params.issuerId, params.name, BASE.TRACE_ID)

          const result = await validatorFuncs.getValidator({ validator, prams: [params.validatorId] })
          utils.assertEqualForEachField(result, {
            ..._.omit(params, ['validatorId']),
            err: '',
          })
        })
      })
    })

    describe('Not normal', () => {
      before(async () => {
        ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
      })

      describe('issuer is not validator in _FINANCIAL_ZONE', () => {
        before(async () => {
          await providerFuncs.addProvider({
            provider,
            accounts,
            options: {
              zoneId: BASE.ZONE_ID.ID0,
            },
          })
          await providerFuncs.addProviderRole({
            provider,
            accounts,
            options: {
              providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
            },
          })
        })

        it('should revert when issuerId is not validator', async () => {
          const result = validatorFuncs.addValidator({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              name: BASE.VALID.VALID0.NAME,
              issuerId: BASE.ISSUER.EMPTY.ID,
            },
          })
          await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
        })

        it('should revert when issuerId is not exist', async () => {
          const result = validatorFuncs.addValidator({
            validator,
            accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              name: BASE.VALID.VALID0.NAME,
              issuerId: toBytes32('0x1234'),
            },
          })
          await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('providerが未登録の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { name: BASE.VALID.VALID0.NAME },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_EXIST)
      })
    })

    describe('provider, providerRole, issuerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { eoaKey: BASE.EOA.ISSUER1 },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { deadline: exceededDeadline },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({ validator, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('空validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { validatorId: BASE.VALID.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { issuerId: BASE.ISSUER.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })
    })

    describe('validatorが登録されている状態', () => {
      before(async () => {
        await validatorFuncs.addValidator({ validator, accounts })
      })

      it('同一validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({ validator, accounts })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_EXIST)
      })

      it('同一IssuerIDを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidator({
          validator,
          accounts,
          options: { validatorId: BASE.VALID.VALID1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_EXIST)
      })
    })
  })
})
