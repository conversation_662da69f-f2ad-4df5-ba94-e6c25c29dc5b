import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

describe('hasValidator()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
      })

      it('validatorが存在すること', async () => {
        const result = await validatorFuncs.hasValidator({ validator, prams: [BASE.VALID.VALID0.ID] })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('空validatorIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.hasValidator({ validator, prams: [utils.toBytes32('')] })

        utils.assertEqualForEachField(result, { success: false, err: ERR.VALID.VALIDATOR_INVALID_VAL })
      })

      it('未登録validatorIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.hasValidator({ validator, prams: [utils.toBytes32('x299')] })

        utils.assertEqualForEachField(result, { success: false, err: ERR.VALID.VALIDATOR_ID_NOT_EXIST })
      })
    })
  })
})
