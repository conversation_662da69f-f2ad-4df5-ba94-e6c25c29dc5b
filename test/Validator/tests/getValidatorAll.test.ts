import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('getValidatorAll()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('Validatorsが登録されている状態', () => {
      let addValidatorAndAccountParams

      const assertList = (result: PromiseType<ReturnType<typeof validatorFuncs.getValidatorAll>>, expected) => {
        assert.isString(result.role, 'role')
        utils.assertEqualForEachField(result, {
          validatorId: expected.validator.ID,
          name: expected.validator.NAME,
          issuerId: expected.issuer.ID,
          enabled: true,
          validatorIdExistence: true,
          issuerIdLinkedFlag: true,
          validatorEoa: expected.validatorEoa,
        })
        expected.accounts.forEach((v, i) => {
          utils.assertEqualForEachField(result.validAccountExistence[i], {
            accountId: v.ID,
            accountIdExistenceByValidatorId: true,
          })
        })
      }

      before(async () => {
        addValidatorAndAccountParams = [
          {
            validator: BASE.VALID.VALID0,
            issuer: BASE.ISSUER.ISSUER0,
            accounts: [],
            validatorEoa: await accounts[0].getAddress(),
          },
          {
            validator: BASE.VALID.VALID1,
            issuer: BASE.ISSUER.ISSUER1,
            accounts: [],
            validatorEoa: await accounts[1].getAddress(),
          },
          {
            validator: BASE.VALID.VALID2,
            issuer: BASE.ISSUER.ISSUER2,
            accounts: [BASE.ACCOUNT.ACCOUNT1, BASE.ACCOUNT.ACCOUNT2],
            validatorEoa: await accounts[2].getAddress(),
          },
          {
            validator: BASE.VALID.VALID3,
            issuer: BASE.ISSUER.ISSUER3,
            accounts: [],
            validatorEoa: await accounts[3].getAddress(),
          },
          {
            validator: BASE.VALID.VALID4,
            issuer: BASE.ISSUER.ISSUER4,
            accounts: [],
            validatorEoa: await accounts[4].getAddress(),
          },
          {
            validator: BASE.VALID.VALID5,
            issuer: BASE.ISSUER.ISSUER5,
            accounts: [],
            validatorEoa: await accounts[5].getAddress(),
          },
          {
            validator: BASE.VALID.VALID6,
            issuer: BASE.ISSUER.ISSUER6,
            accounts: [],
            validatorEoa: await accounts[6].getAddress(),
          },
          {
            validator: BASE.VALID.VALID7,
            issuer: BASE.ISSUER.ISSUER7,
            accounts: [],
            validatorEoa: await accounts[7].getAddress(),
          },
          {
            validator: BASE.VALID.VALID8,
            issuer: BASE.ISSUER.ISSUER8,
            accounts: [],
            validatorEoa: await accounts[8].getAddress(),
          },
          {
            validator: BASE.VALID.VALID9,
            issuer: BASE.ISSUER.ISSUER9,
            accounts: [],
            validatorEoa: await accounts[9].getAddress(),
          },
          {
            validator: BASE.VALID.VALID10,
            issuer: BASE.ISSUER.ISSUER10,
            accounts: [],
            validatorEoa: await accounts[10].getAddress(),
          },
          {
            validator: BASE.VALID.VALID11,
            issuer: BASE.ISSUER.ISSUER11,
            accounts: [],
            validatorEoa: await accounts[11].getAddress(),
          },
          {
            validator: BASE.VALID.VALID12,
            issuer: BASE.ISSUER.ISSUER12,
            accounts: [],
            validatorEoa: await accounts[12].getAddress(),
          },
          {
            validator: BASE.VALID.VALID13,
            issuer: BASE.ISSUER.ISSUER13,
            accounts: [],
            validatorEoa: await accounts[13].getAddress(),
          },
          {
            validator: BASE.VALID.VALID14,
            issuer: BASE.ISSUER.ISSUER14,
            accounts: [],
            validatorEoa: await accounts[14].getAddress(),
          },
          {
            validator: BASE.VALID.VALID15,
            issuer: BASE.ISSUER.ISSUER15,
            accounts: [],
            validatorEoa: await accounts[15].getAddress(),
          },
          {
            validator: BASE.VALID.VALID16,
            issuer: BASE.ISSUER.ISSUER16,
            accounts: [],
            validatorEoa: await accounts[16].getAddress(),
          },
          {
            validator: BASE.VALID.VALID17,
            issuer: BASE.ISSUER.ISSUER17,
            accounts: [],
            validatorEoa: await accounts[17].getAddress(),
          },
          {
            validator: BASE.VALID.VALID18,
            issuer: BASE.ISSUER.ISSUER18,
            accounts: [],
            validatorEoa: await accounts[18].getAddress(),
          },
          {
            validator: BASE.VALID.VALID19,
            issuer: BASE.ISSUER.ISSUER19,
            accounts: [],
            validatorEoa: await accounts[19].getAddress(),
          },
        ]
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        for (let i = 0; i < addValidatorAndAccountParams.length; i++) {
          await issuerFuncs.addIssuer({
            issuer,
            accounts,
            options: {
              issuerId: addValidatorAndAccountParams[i].issuer.ID,
              name: addValidatorAndAccountParams[i].issuer.NAME,
              bankCode: addValidatorAndAccountParams[i].issuer.BANK_CODE,
              deadline: deadline + 60,
            },
          })
          await validatorFuncs.addValidator({
            validator,
            accounts,
            options: {
              validatorId: addValidatorAndAccountParams[i].validator.ID,
              name: addValidatorAndAccountParams[i].validator.NAME,
              issuerId: addValidatorAndAccountParams[i].issuer.ID,
              deadline: deadline + 60,
            },
          })
          await validatorFuncs.addValidatorRole({
            validator,
            accounts,
            options: {
              validatorId: addValidatorAndAccountParams[i].validator.ID,
              validatorEoa: addValidatorAndAccountParams[i].validatorEoa,
              deadline: deadline + 60,
            },
          })
        }
        await providerFuncs.addToken({ provider, accounts })

        for (const v of addValidatorAndAccountParams) {
          for (const account of v.accounts) {
            await validatorFuncs.addAccount({
              validator,
              accounts,
              options: {
                validatorId: v.validator.ID,
                accountId: account.ID,
              },
            })
          }
        }
      })

      it('登録したValidatorsを取得できること', async () => {
        const count = await validatorFuncs.getValidatorCount({ validator: validator })
        for (let i = 0; i < Number(count); i++) {
          const result = await validatorFuncs.getValidatorAll({ validator, index: i })
          assertList(result, addValidatorAndAccountParams[i])
        }
      })
    })
  })
})
