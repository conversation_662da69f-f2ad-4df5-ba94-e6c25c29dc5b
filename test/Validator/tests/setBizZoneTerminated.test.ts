import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('setBizZoneTerminated()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, contractManager } =
        await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const account of [
          BASE.ACCOUNT.ACCOUNT1,
          BASE.ACCOUNT.ACCOUNT2,
          BASE.ACCOUNT.ACCOUNT3,
          BASE.ACCOUNT.ACCOUNT4,
        ]) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: account.ID },
          })
        }
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })
      })

      it('FinZone管理のBizZoneアカウントを解約できること', async () => {
        const tx = await validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const expectedFin = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: '0',
          reasonCode: BASE.REASON_CODE1,
          appliedAt: '0',
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: '0',
          terminatedAt: '0',
          mintLimit: '3000',
          burnLimit: '4000',
          chargeLimit: '2000',
          dischargeLimit: '4000',
          transferLimit: '1000',
          cumulativeLimit: '5000',
          cumulativeAmount: '0',
          cumulativeDate: '0',
        }

        const expectedBiz = [
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneName: BASE.ZONE_NAME.NAME1,
            balance: '0',
            accountStatus: BASE.STATUS.TERMINATED,
            appliedAt: await utils.getLatestBlockTimestamp(),
            registeredAt: await utils.getLatestBlockTimestamp(),
            terminatingAt: await utils.getLatestBlockTimestamp(),
            terminatedAt: await utils.getLatestBlockTimestamp(),
          },
        ]

        // BizZoneのアカウントが解約されたことを確認
        const result = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result.accountDataAll, expectedFin)
        utils.assertEqualForEachField(
          result.accountDataAll.businessZoneAccounts.map((v) => {
            return {
              accountName: v.accountName,
              zoneName: v.zoneName,
              balance: v.balance,
              accountStatus: v.accountStatus,
              appliedAt: v.appliedAt,
              registeredAt: v.registeredAt,
              terminatingAt: v.terminatingAt,
              terminatedAt: v.terminatedAt,
            }
          }),
          expectedBiz,
        )

        const expectParams = {
          validatorId: BASE.VALID.VALID0.ID,
          zoneId: BASE.ZONE_ID.ID1,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(validator, 'SetBizZoneTerminated')
          .withArgs(...Object.values(expectParams))
        assert.equal(result.err, '')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, businessZoneAccount } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const account of [
          BASE.ACCOUNT.ACCOUNT1,
          BASE.ACCOUNT.ACCOUNT2,
          BASE.ACCOUNT.ACCOUNT3,
          BASE.ACCOUNT.ACCOUNT4,
        ]) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: account.ID },
          })
        }
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })
      })

      it('存在しないアカウントを指定した場合、エラー終了すること', async () => {
        const result = validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('should revert when zoneId is invalid', async () => {
        const result = validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.EMPTY_ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })
})
