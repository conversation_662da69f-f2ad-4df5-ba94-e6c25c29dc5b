import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  AddValidatorAccountIdOption,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('addValidatorAccountId()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('Validator管理のアカウントIDを追加できること', async () => {
        const params: AddValidatorAccountIdOption = {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        }

        const tx = await validatorFuncs.addValidatorAccountId({
          validator,
          accounts,
          options: params,
        })
        await expect(tx)
          .to.emit(validator, 'AddValidatorAccountId')
          .withArgs(params.validatorId, params.accountId, BASE.TRACE_ID)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<ValidatorContractType>())
    })

    describe('初期状態', () => {
      it('validatorが未登録の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidatorAccountId({ validator, accounts })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_NOT_EXIST)
      })
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
      })

      it('accountが未登録の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidatorAccountId({ validator, accounts })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('空validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidatorAccountId({
          validator,
          accounts,
          options: { validatorId: BASE.VALID.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidatorAccountId({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('紐づいていないaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addValidatorAccountId({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<ValidatorContractType>())
    })

    describe('account exist in contract account but not in validator', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
      })

      it('should revert when account exist in contract account but not in validator', async () => {
        // Hack: Fake validator call to Account.addAccount
        // This case happen when account is added to contract account but not to validator
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        await account.connect(fakeValidator).addAccount(
          // Fake validator call to Account.addAccount
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT1.NAME,
          BASE.VALID.VALID0.ID,
        )

        const result = validatorFuncs.addValidatorAccountId({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
