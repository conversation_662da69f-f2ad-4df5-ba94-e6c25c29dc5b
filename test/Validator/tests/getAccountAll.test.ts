import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAccountAll()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, contractManager } =
        await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const account of [
          BASE.ACCOUNT.ACCOUNT1,
          BASE.ACCOUNT.ACCOUNT2,
          BASE.ACCOUNT.ACCOUNT3,
          BASE.ACCOUNT.ACCOUNT4,
        ]) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: account.ID },
          })
        }
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('紐づくBusinessZone情報も含め、アカウントの全情報が取得できること', async () => {
        const expectedFin = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: '0',
          reasonCode: BASE.REASON_CODE1,
          zoneId: String(BASE.ZONE_ID.ID0),
          zoneName: BASE.ZONE_NAME.NAME0,
          appliedAt: '0',
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: '0',
          terminatedAt: '0',
          mintLimit: '3000',
          burnLimit: '4000',
          chargeLimit: '2000',
          dischargeLimit: '4000',
          transferLimit: '1000',
          cumulativeLimit: '5000',
          cumulativeAmount: '0',
          cumulativeDate: '0',
        }

        const expectedBiz = [
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneId: String(BASE.ZONE_ID.ID1),
            zoneName: BASE.ZONE_NAME.NAME1,
            balance: '0',
            accountStatus: BASE.STATUS.ACTIVE,
            appliedAt: await utils.getLatestBlockTimestamp(),
            registeredAt: await utils.getLatestBlockTimestamp(),
            terminatingAt: '0',
            terminatedAt: '0',
          },
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneId: String(BASE.ZONE_ID.ID2),
            zoneName: BASE.ZONE_NAME.NAME2,
            balance: '0',
            accountStatus: BASE.STATUS.ACTIVE,
            appliedAt: await utils.getLatestBlockTimestamp(),
            registeredAt: await utils.getLatestBlockTimestamp(),
            terminatingAt: '0',
            terminatedAt: '0',
          },
        ]

        const result = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result.accountDataAll, expectedFin)
        utils.assertEqualForEachField(
          result.accountDataAll.businessZoneAccounts.map((v) => {
            return {
              accountName: v.accountName,
              zoneId: v.zoneId,
              zoneName: v.zoneName,
              balance: v.balance,
              accountStatus: v.accountStatus,
              appliedAt: v.appliedAt,
              registeredAt: v.registeredAt,
              terminatingAt: v.terminatingAt,
              terminatedAt: v.terminatedAt,
            }
          }),
          expectedBiz,
        )
        assert.equal(result.err, '')
      })

      it('存在しないアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT20.ID],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空のアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.EMPTY.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL)
      })
    })
  })
})
