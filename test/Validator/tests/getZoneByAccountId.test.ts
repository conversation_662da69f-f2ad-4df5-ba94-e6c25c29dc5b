import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

describe('getZoneByAccountId()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, contractManager } =
        await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const account of [
          BASE.ACCOUNT.ACCOUNT1,
          BASE.ACCOUNT.ACCOUNT2,
          BASE.ACCOUNT.ACCOUNT3,
          BASE.ACCOUNT.ACCOUNT4,
        ]) {
          await validatorFuncs.addAccount({
            validator,
            accounts,
            options: { accountId: account.ID },
          })
        }
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('zone情報のリストが取得できること', async () => {
        const expectedZoneInfo = [
          { zoneId: BASE.ZONE_ID.ID1, zoneName: BASE.ZONE_NAME.NAME1 },
          { zoneId: BASE.ZONE_ID.ID2, zoneName: BASE.ZONE_NAME.NAME2 },
        ]

        const result = await validatorFuncs.getZoneByAccountId({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expectedZoneInfo.forEach((v, i) => {
          utils.assertEqualForEachField(result.zones[i], {
            zoneId: v.zoneId.toString(),
            zoneName: v.zoneName,
          })
        })
      })
    })
  })
})
