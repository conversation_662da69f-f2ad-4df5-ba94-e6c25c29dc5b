import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modValidator()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
      })

      it('validatorが更新できること', async () => {
        const validatorId = BASE.VALID.VALID0.ID
        const newValidName = BASE.VALID.VALID0.NAME

        let tx = await validatorFuncs.modValidator({
          validator,
          accounts,
          name: newValidName,
          options: { validatorId },
        })

        const expectParams = {
          validatorId,
          name: newValidName,
        }
        await expect(tx)
          .to.emit(validator, 'ModValidator')
          .withArgs(...Object.values(expectParams), BASE.TRACE_ID)

        let result = await validatorFuncs.getValidator({ validator, prams: [validatorId] })
        utils.assertEqualForEachField(result, {
          name: newValidName,
          issuerId: BASE.ISSUER.ISSUER0.ID,
          err: '',
        })

        // 検証者名を指定しないときは元の値のまま
        tx = await validatorFuncs.modValidator({
          validator,
          accounts,
          name: BASE.VALID.EMPTY.NAME,
          options: { validatorId },
        })

        const expectParams2 = {
          validatorId,
          name: BASE.VALID.EMPTY.NAME,
        }
        await expect(tx)
          .to.emit(validator, 'ModValidator')
          .withArgs(...Object.values(expectParams2), BASE.TRACE_ID)

        result = await validatorFuncs.getValidator({ validator, prams: [validatorId] })
        utils.assertEqualForEachField(result, {
          name: BASE.VALID.EMPTY.NAME,
          issuerId: BASE.ISSUER.ISSUER0.ID,
          err: '',
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
      })

      it('未登録validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.modValidator({
          validator,
          accounts,
          name: BASE.VALID.VALID0.NAME,
          options: {
            validatorId: utils.toBytes32('x299'),
          },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_NOT_EXIST)
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.modValidator({
          validator,
          accounts,
          name: BASE.VALID.VALID0.NAME,
          options: { eoaKey: BASE.EOA.PROV2 },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.modValidator({
          validator,
          accounts,
          name: BASE.VALID.VALID0.NAME,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })
  })
})
