import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('modIssuer()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('issuer情報が変更できること、発行者名を指定しない場合、発行者名が元の値のままであること', async () => {
        // 発行者名
        const params = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          name: BASE.ISSUER.ISSUER1.NAME,
        }

        let tx = await issuerFuncs.modIssuer({ issuer, accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'ModIssuer')
          .withArgs(...Object.values(params), anyValue)
        let result = await issuerFuncs.getIssuer({ issuer, params: [params.issuerId] })
        assertEqualForEachField(result, { name: params.name, err: '' })

        // 発行者名を指定しないときは元の値のまま
        params.name = BASE.ISSUER.EMPTY.NAME

        tx = await issuerFuncs.modIssuer({ issuer, accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'ModIssuer')
          .withArgs(...Object.values(params), anyValue)
        result = await issuerFuncs.getIssuer({ issuer, params: [params.issuerId] })
        assertEqualForEachField(result, { name: BASE.ISSUER.ISSUER1.NAME, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('未登録issuerIdを指定する場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modIssuer({ issuer, accounts, options: { issuerId: NOT_REG_ISSUER_ID } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('Admin権限がない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modIssuer({ issuer, accounts, options: { eoaKey: BASE.EOA.ISSUER1 } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modIssuer({ issuer, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })
  })
})
