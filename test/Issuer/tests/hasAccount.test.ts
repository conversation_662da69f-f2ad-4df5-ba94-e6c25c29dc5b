import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

describe('hasAccount()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('issuerに紐づくaccountが存在する場合、trueが取得できること', async () => {
        const result = await issuerFuncs.hasAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assertEqualForEachField(result, { success: true, err: '' })
      })

      it('紐付けられていないaccount指定の場合、エラーが返されること', async () => {
        const result = await issuerFuncs.hasAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, toBytes32('x110')],
        })

        assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('空accountを指定した場合、エラーが返されること', async () => {
        const result = await issuerFuncs.hasAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.EMPTY.ID],
        })

        assertEqualForEachField(result, { success: false, err: ERR.COMMON.INVALID_ACCOUNT_ID })
      })
    })
  })
})
