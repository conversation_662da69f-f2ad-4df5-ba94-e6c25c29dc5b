import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const TERMINATED_ACCOUNT_ID = BASE.ACCOUNT.ACCOUNT6.ID

describe('getAccountList()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      const prams = [
        BASE.ACCOUNT.ACCOUNT1,
        BASE.ACCOUNT.ACCOUNT2,
        BASE.ACCOUNT.ACCOUNT3,
        BASE.ACCOUNT.ACCOUNT4,
        BASE.ACCOUNT.ACCOUNT5,
      ]
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (let i = 0; i < prams.length; i++) {
          validatorFuncs.addAccount({ validator, accounts, options: { accountId: prams[i].ID } })
        }
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
        await validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
      })

      it('指定したissuerIdに紐づくaccountリストが取得できること', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = [
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
          TERMINATED_ACCOUNT_ID,
        ]
        const expectedAccountInfo = [
          {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT4.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: TERMINATED_ACCOUNT_ID,
            balance: 0,
            accountStatus: BASE.STATUS.TERMINATED,
            reasonCode: BASE.REASON_CODE2,
          },
        ]

        const result = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 5, 0] })
        utils.assertEqualForEachField(result, { totalCount: 6, err: '' })
        expectedAccountInfo.forEach((v, i) => {
          utils.assertEqualForEachField(result.accounts[i], {
            accountId: v.accountId,
            balance: v.balance.toString(),
            accountStatus: v.accountStatus,
            reasonCode: v.reasonCode.toString(),
          })
        })
      })

      it('inAccountIdsに存在しないaccountIdが含まれる場合、空データが取得できること', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = [
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          utils.toBytes32('x900'),
          BASE.ACCOUNT.ACCOUNT4.ID,
          BASE.ACCOUNT.ACCOUNT5.ID,
        ]
        const expectedAccountInfo = [
          {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.EMPTY.ID,
            balance: 0,
            accountStatus: utils.toBytes32(''),
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT4.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
          {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            balance: 0,
            accountStatus: BASE.STATUS.ACTIVE,
            reasonCode: BASE.REASON_CODE1,
          },
        ]

        const result = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 5, 0] })

        utils.assertEqualForEachField(result, { totalCount: 6, err: '' })
        expectedAccountInfo.forEach((v, i) => {
          utils.assertEqualForEachField(result.accounts[i], {
            accountId: v.accountId,
            balance: v.balance.toString(),
            accountStatus: v.accountStatus,
            reasonCode: v.reasonCode.toString(),
          })
        })
      })

      it('空のaccountリストでリクエストした場合、空リストが取得できること', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = []

        const result = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 3, 0] })

        utils.assertEqualForEachField(result, {
          accounts: [],
          totalCount: 0,
          err: '',
        })
      })

      it('issuerに紐づくaccountがない場合、エラーがスローされること', async () => {
        const issuerId = BASE.ISSUER.ISSUER1.ID
        const inAccountIds = [BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]

        const result = issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 5, 0] })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuer is registered but params invalid', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('should return empty data when limit = 0', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = [
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
          BASE.ACCOUNT.ACCOUNT5.ID,
        ]
        const tx = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 0, 0] })

        assert.equal(tx.totalCount, 0)
      })

      it('should return error too large limit when limit > _MAX_LIMIT', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = [
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
          BASE.ACCOUNT.ACCOUNT5.ID,
        ]
        const tx = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 101, 0] })

        assert.equal(tx.err, ERR.VALID.VALIDATOR_TOO_LARGE_LIMIT)
      })

      it('should return error offset out of index when offset >= inAccountIds.length', async () => {
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const inAccountIds = [
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
          BASE.ACCOUNT.ACCOUNT5.ID,
        ]
        const tx = await issuerFuncs.getAccountList({ issuer, params: [issuerId, inAccountIds, 5, 5] })

        assert.equal(tx.err, ERR.VALID.VALIDATOR_OFFSET_OUT_OF_INDEX)
      })
    })
  })
})
