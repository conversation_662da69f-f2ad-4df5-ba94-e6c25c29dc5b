import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('checkRole()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuer, issuerRoleが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
      })

      it('roleがある場合、trueが取得できること', async () => {
        const result = await issuerFuncs.checkRole({ issuer })

        assertEqualForEachField(result, { has: true, err: '' })
      })

      it('roleがない場合、falseが取得できること', async () => {
        const result = await issuerFuncs.checkRole({ issuer, options: { eoaKey: BASE.EOA.ISSUER2 } })

        assertEqualForEachField(result, { has: false, err: '' })
      })

      it('空issuerIdを指定した場合、エラーが返されること', async () => {
        const result = await issuerFuncs.checkRole({ issuer, options: { issuerId: BASE.ISSUER.EMPTY.ID } })

        assert.equal(result.err, ERR.ISSUER.ISSUER_INVALID_VAL, 'err')
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = await issuerFuncs.checkRole({ issuer, options: { deadline: exceededDeadline } })

        assert.equal(result.err, ERR.ACTRL.ACTRL_SIG_TIMEOUT, 'err')
      })
    })

    describe('issuerが無効の状態', () => {
      it('roleがある場合、trueが取得できること', async () => {
        const result = await issuerFuncs.checkRole({ issuer, options: { issuerId: BASE.ISSUER.ISSUER0.ID } })

        assertEqualForEachField(result, { has: true, err: '' })
      })
    })
  })
})
