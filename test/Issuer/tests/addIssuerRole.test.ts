import { BASE, ERR } from '@test/common/consts'
import { AddIssuerRoleOption, IssuerInstance } from '@test/common/types'
import { toBytes32, getExceededDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const TERMINATED_ACCOUNT_ID = BASE.ACCOUNT.ACCOUNT6.ID
const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('addIssuerRole()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
            issuerId: BASE.ISSUER.ISSUER1.ID,
            name: BASE.ISSUER.ISSUER1.NAME,
          },
        })
      })

      it('issuerRoleが追加されること', async () => {
        const params: AddIssuerRoleOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          issuerEoa: await accounts[BASE.EOA.ISSUER1].getAddress(),
        }

        const tx = await issuerFuncs.addIssuerRole({ issuer, accounts, options: params })
        await expect(tx)
          .to.emit(issuer, 'AddIssuerRole')
          .withArgs(...Object.values(params), anyValue)
      })

      it('issuerRoleが追加されること（issuerIdが違う）', async () => {
        const params: AddIssuerRoleOption = {
          issuerId: BASE.ISSUER.ISSUER1.ID,
          issuerEoa: await accounts[BASE.EOA.ISSUER2].getAddress(),
        }

        const tx = await issuerFuncs.addIssuerRole({ issuer, accounts, options: params })
        await expect(tx)
          .to.emit(issuer, 'AddIssuerRole')
          .withArgs(...Object.values(params), anyValue)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addIssuerRole({ issuer, accounts, options: { eoaKey: BASE.EOA.ISSUER1 } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()
        await expect(
          issuerFuncs.addIssuerRole({ issuer, accounts, options: { deadline: exceededDeadline } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addIssuerRole({ issuer, accounts, options: { sig: ['0x2345', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('空EOA指定の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addIssuerRole({
            issuer,
            accounts,
            options: { issuerEoa: '0x0000000000000000000000000000000000000000' },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('未登録issuerId指定の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addIssuerRole({ issuer, accounts, options: { issuerId: NOT_REG_ISSUER_ID } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })
    })
  })
})
