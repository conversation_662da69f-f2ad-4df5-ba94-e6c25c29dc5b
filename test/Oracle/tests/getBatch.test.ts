import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { oracleFuncs } from '@test/Oracle/helpers/function'
import { OracleContractType } from '@test/Oracle/helpers/types'
import chai from 'chai'
import { before } from 'mocha'

describe('getBatch()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]

  const oracleId = 2
  const keys = [toBytes32('0x01'), toBytes32('0x02')]
  const values = [toBytes32('0x64'), toBytes32('0x65')]

  describe('正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('OracleValueが複数登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
        await oracle.connect(invoker).setBatch(oracleId, keys, values)
      })

      it('keysに紐づくOracleValueの値が返却されること', async () => {
        const result = await oracleFuncs.getBatch({
          oracle,
          prams: [oracleId, [...keys, toBytes32('0x03')]],
        })
        // Property of result is naming "values" but it is conflicting with the values function of object
        // Changed to 0, 1 to avoid conflict
        values.push(toBytes32(''))
        const expectParams = {
          0: values,
          1: '',
        }
        for (const [key, value] of Object.entries(result)) {
          chai.assert.deepEqual(value, expectParams[key])
        }
      })

      it('oracleId = 0 を指定した場合、エラーが返却されること', async () => {
        const result = await oracleFuncs.getBatch({ oracle, prams: [0, keys] })
        // Property of result is naming "values" but it is conflicting with the values function of object
        // Changed to 0, 1 to avoid conflict
        const expectParams = {
          0: [],
          1: ERR.ORACLE.ORACLE_INVALID_VAL,
        }
        for (const [key, value] of Object.entries(result)) {
          chai.assert.deepEqual(value, expectParams[key])
        }
      })
    })
  })
})
