import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { oracleFuncs } from '@test/Oracle/helpers/function'
import { OracleContractType } from '@test/Oracle/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('set()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]

  const oracleId = 2
  const key = toBytes32('0x01')
  const value = toBytes32('0x64')

  describe('正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
      })

      it('OracleValuesが設定されること', async () => {
        const tx = await oracle.connect(invoker).set(oracleId, key, value)

        await expect(tx).to.emit(oracle, 'SetOracleValue').withArgs(oracleId, key, value)
        const result = await oracleFuncs.get({ oracle, prams: [oracleId, key] })
        assertEqualForEachField(result, { err: '', value })
      })

      it('同一keyに対してOracleValuesが設定されている場合、イベントが発火されないこと', async () => {
        const tx = await oracle.connect(invoker).set(oracleId, key, value)

        await expect(tx).to.not.emit(oracle, 'SetOracleValue')
      })
    })
  })

  describe('準正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
      })

      it('未登録invokerを指定した場合、エラーがスローされること', async () => {
        const result = oracle.connect(accounts[2]).set(oracleId, key, value)
        await expect(result).to.be.revertedWith(ERR.ORACLE.NOT_INVOKER_ADDRESS)
      })
    })
  })
})
