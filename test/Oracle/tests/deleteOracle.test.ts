import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { OracleContractType } from '@test/Oracle/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('deleteOracle()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]
  const oracleId = 2

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await accounts[1].getAddress())
      })

      it('oracleが削除されること', async () => {
        const tx = await oracle.deleteOracle(oracleId)

        await expect(tx).to.emit(oracle, 'DeleteOracle').withArgs(oracleId)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await accounts[1].getAddress())
      })

      it('oracleId = 0 を指定した場合、エラーがスローされること', async () => {
        const result = oracle.deleteOracle(0)
        await expect(result).to.be.revertedWith(ERR.ORACLE.ORACLE_INVALID_VAL)
      })
    })
  })
})
