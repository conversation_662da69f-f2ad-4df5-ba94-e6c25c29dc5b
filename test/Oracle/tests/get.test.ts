import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { oracleFuncs } from '@test/Oracle/helpers/function'
import { OracleContractType } from '@test/Oracle/helpers/types'
import { before } from 'mocha'

describe('get()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]

  const oracleId = 2
  const keys = [toBytes32('0x01'), toBytes32('0x02')]
  const values = [toBytes32('0x64'), toBytes32('0x65')]

  describe('正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('OracleValueが複数登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
        await oracle.connect(invoker).setBatch(oracleId, keys, values)
      })

      it('keyに紐づくOracleValueの値が返却されること', async () => {
        const result = await oracleFuncs.get({ oracle, prams: [oracleId, keys[1]] })

        assertEqualForEachField(result, { err: '', value: values[1] })
      })

      it('存在しないkeyを指定した場合、0x00が返却されること', async () => {
        const result = await oracleFuncs.get({ oracle, prams: [oracleId, toBytes32('0x03')] })

        assertEqualForEachField(result, {
          err: '',
          value: toBytes32(''),
        })
      })

      it('oracleId = 0 を指定した場合、エラーが返却されること', async () => {
        const result = await oracleFuncs.get({ oracle, prams: [0, keys[1]] })

        assertEqualForEachField(result, {
          err: ERR.ORACLE.ORACLE_INVALID_VAL,
          value: toBytes32(''),
        })
      })
    })
  })
})
