import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { OracleContractType } from '@test/Oracle/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addOracle()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]
  const oracleId = 2

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
    })

    describe('初期状態', () => {
      it('oracleが登録されること', async () => {
        const invoker = await accounts[1]
        const tx = await oracle.addOracle(oracleId, await invoker.getAddress())

        await expect(tx)
          .to.emit(oracle, 'AddOracle')
          .withArgs(oracleId, await invoker.getAddress())
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
    })

    describe('初期状態', () => {
      it('oracleId = 0 を指定した場合、エラーがスローされること', async () => {
        const result = oracle.addOracle(0, await accounts[1].getAddress())
        await expect(result).to.be.revertedWith(ERR.ORACLE.ORACLE_INVALID_VAL)
      })

      it('invokerに空アドレスを指定した場合、エラーがスローされること', async () => {
        const result = oracle.addOracle(oracleId, '0x0000000000000000000000000000000000000000')
        await expect(result).to.be.revertedWith(ERR.ORACLE.INVOKER_INVALID_VAL)
      })
    })
  })
})
