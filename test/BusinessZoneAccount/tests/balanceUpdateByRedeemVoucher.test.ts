import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'
import { BASE, ERR } from '@test/common/consts'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Tokenコントラクトで利用
// balanceUpdateByRedeemVoucherの雛形のみ作成
describe('balanceUpdateByRedeemVoucher()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]
  // balanceUpdateByRedeemVoucherのテストは、token.issuerVoucherから呼ばれるため ここでは呼び出し元の検証のみを行う

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager, ibcToken } =
      await contractFixture<BusinessZoneAccountContractType>())
  }

  describe('正常系', () => {
    let ibcAddress
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0]
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        await expect(
          businessZoneAccount
            .connect(accounts[0])
            .balanceUpdateByRedeemVoucher(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID, 100, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })

    describe('balance should be update when function success', () => {
      let ibcAddress
      before(async () => {
        ibcAddress = await accounts[0]
        const ibcAddressString = await ibcAddress.getAddress()

        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })

        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })

        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('redeem voucher success from token contract', async () => {
        const result = await ibcToken.connect(accounts[0]).syncBusinessZoneBalance({
          fromZoneId: BASE.ZONE_ID.ID1,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          amount: 100,
          traceId: BASE.TRACE_ID,
        })
        assertEqualForEachField(result, {})
        // Hack: Fake token can call balanceUpdateByRedeemVoucher
        // UNREACHABLE: This function didn't call from anywhere
        // Impersonate token contract because none of the other contract call
        // TODO: This should be change when there's an new integrate of this function from other contract
        const tokenAddress = await token.getAddress()
        await helpers.setBalance(tokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(tokenAddress)
        const fakeToken = await ethers.getSigner(tokenAddress)
        // Call the function
        const tx = await businessZoneAccount
          .connect(fakeToken)
          .balanceUpdateByRedeemVoucher(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)
        const expectParams = {
          zoneId: BASE.ZONE_ID.ID1,
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(businessZoneAccount, 'BalanceUpdateByRedeemVoucher')
          .withArgs(...Object.values(expectParams))
        await helpers.stopImpersonatingAccount(tokenAddress)
      })
    })
  })
  describe('準正常系', () => {
    let ibcAddress
    before(async () => {
      ibcAddress = await accounts[0]
      await setupFixture()
    })

    describe('invalid data in account', () => {
      before(async () => {
        ibcAddress = await accounts[0]
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })

        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('should revert when account balance is not enough', async () => {
        const result = await ibcToken.connect(accounts[0]).syncBusinessZoneBalance({
          fromZoneId: BASE.ZONE_ID.ID1,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          toAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          amount: 100,
          traceId: BASE.TRACE_ID,
        })
        assertEqualForEachField(result, {})
        // Hack: Fake token can call balanceUpdateByRedeemVoucher
        // UNREACHABLE: This function didn't call from anywhere
        // Impersonate token contract because none of the other contract call
        // TODO: This should be change when there's an new integrate of this function from other contract
        const tokenAddress = await token.getAddress()
        await helpers.setBalance(tokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(tokenAddress)
        const fakeToken = await ethers.getSigner(tokenAddress)
        // Call the function
        await expect(
          businessZoneAccount
            .connect(fakeToken)
            .balanceUpdateByRedeemVoucher(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID, 500, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.ACCOUNT.BALANCE_NOT_ENOUGH)

        helpers.stopImpersonatingAccount(tokenAddress)
      })
    })
  })
})
