import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'

describe('syncBusinessZoneBalance()', () => {
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager, ibcToken } =
      await contractFixture<BusinessZoneAccountContractType>())
  }

  describe('正常系', () => {
    let ibcAddress
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })

        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })
      it('呼び出し元がIBCの場合、エラーがスローされないこと', async () => {
        const result = await ibcToken.connect(accounts[0]).syncBusinessZoneBalance({
          fromZoneId: BASE.ZONE_ID.ID1,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          amount: 100,
          traceId: BASE.TRACE_ID,
        })
        assertEqualForEachField(result, {})
      })
      it('呼び出し元がIBCの場合、イベントが発行されていること', async () => {
        const tx = await ibcTokenFuncs.syncBusinessZoneBalance({
          ibcToken,
          from: ibcAddress,
          prams: {
            fromZoneId: BASE.ZONE_ID.ID1,
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            amount: 100,
            traceId: BASE.TRACE_ID,
          },
        })
        const expectParams = {
          transferData: {
            transferType: BASE.TOKEN.TRANSFER_TYPE.TRANSFER,
            zoneId: BASE.ZONE_ID.ID1,
            fromValidatorId: BASE.VALID.VALID0.ID,
            toValidatorId: BASE.VALID.VALID0.ID,
            fromAccountBalance: BigInt(100),
            toAccountBalance: BigInt(500),
            businessZoneBalance: BigInt(0),
            bizZoneId: 0,
            sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            amount: BigInt(100),
            miscValue1: toBytes32(''),
            miscValue2: '',
            memo: '',
          },
        }
        await expect(tx)
          .to.emit(businessZoneAccount, 'SyncBusinessZoneBalance')
          .withArgs(Object.values(expectParams.transferData), BASE.TRACE_ID)
        const fromAccountBalance = await businessZoneAccount.getBusinessZoneAccount(
          BASE.ZONE_ID.ID1,
          BASE.ACCOUNT.ACCOUNT0.ID,
        )
        assertEqualForEachField(fromAccountBalance, { balance: 300n - 100n - 100n })
        const toAccountBalance = await businessZoneAccount.getBusinessZoneAccount(
          BASE.ZONE_ID.ID1,
          BASE.ACCOUNT.ACCOUNT1.ID,
        )
        assertEqualForEachField(toAccountBalance, { balance: 300n + 100n + 100n })
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    before(async () => {
      ibcAddress = await accounts[0]
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        await expect(
          businessZoneAccount.connect(accounts[0]).syncBusinessZoneBalance({
            fromZoneId: BASE.ZONE_ID.ID0,
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            fromAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            amount: 100,
            traceId: BASE.TRACE_ID,
          }),
        ).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })

    describe('Fake call from token contract', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })

        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('should pass when fake call from token contract', async () => {
        // Hack: Fake token can call syncBusinessZoneBalance
        // UNREACHABLE: Call move from token to ibcToken so token case never reach
        await helpers.impersonateAccount(await token.getAddress())
        await helpers.setBalance(await token.getAddress(), 100n ** 18n)
        const fakeToken = await ethers.getSigner(await token.getAddress())
        const result = await businessZoneAccount.connect(fakeToken).syncBusinessZoneBalance({
          fromZoneId: BASE.ZONE_ID.ID1,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          amount: 100,
          traceId: BASE.TRACE_ID,
        })
        assertEqualForEachField(result, {})
        await helpers.stopImpersonatingAccount(await token.getAddress())
      })
    })
  })
})
